{"name": "bog-adani-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.0", "@angular/cdk": "^19.2.7", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/google-maps": "^20.1.3", "@angular/material": "^19.2.1", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ng-select/ng-select": "^14.2.6", "@popperjs/core": "^2.11.8", "apexcharts": "^4.5.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "d3": "^7.9.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "ng-apexcharts": "^1.15.0", "ng-otp-input": "^2.0.6", "ngx-quill": "^27.0.1", "quill": "^2.0.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.0", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "@types/bootstrap": "^5.2.10", "@types/d3": "^7.4.3", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}