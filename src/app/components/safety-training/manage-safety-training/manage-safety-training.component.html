<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Safety Training Records</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadObsExcelDropdown0"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || isLoading">
                        <span *ngIf="!isDownloadingExcel"> <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel"> <span class="spinner-border spinner-border-sm me-1"
                                role="status" aria-hidden="true"></span> Downloading {{ downloadType === 'current' ?
                            'Page' : 'All' }}... </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadObsExcelDropdown0">
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoading || (safetyTrainingRecords?.length ?? 0) === 0">
                                <span *ngIf="!isDownloadingExcel || downloadType !== 'current'">
                                    <i class="bi bi-download me-1"></i> Download Current Page ({{ safetyTrainingRecords?.length ?? 0 }})
                                </span>
                                <span *ngIf="isDownloadingExcel && downloadType === 'current'">
                                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Downloading...
                                </span>
                            </button> </li>
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoading || totalItems === 0"> 
                                <span *ngIf="!isDownloadingExcel || downloadType !== 'all'">
                                    <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                                </span>
                                <span *ngIf="isDownloadingExcel && downloadType === 'all'">
                                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Downloading...
                                </span>
                            </button> </li>
                    </ul>
                </div>
                <!-- Add Training Button -->
                <button type="button" class="btn-sm adani-btn me-2" (click)="openCreateModal()"
                    title="Add New Safety Training Record">
                    <i class="bi bi-plus-circle"></i> Add Record
                </button>
                <!-- Filter Button -->
                <button type="button" class="btn btn-light ms-3 p-1" (click)="openFilterModal()" title="Filter Records">
                    <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-header">
                        <tr>
                            <th>Basic Info</th>
                            <th>Training Details</th>
                            <th>Participants</th>
                            <th>Totals & Manhours</th>
                            <!-- <th>Contractor Participants</th> -->
                            <th>Feedback</th>
                            <th style="padding:8px 12px 8px 12px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Loading State -->
                        <tr *ngIf="isLoading">
                            <td colspan="7" class="text-center p-4"> <!-- Updated colspan -->
                                <span class="spinner-border spinner-border-sm"></span>
                                Loading safety training records...
                            </td>
                        </tr>
                        <!-- No Data State -->
                        <tr *ngIf="!isLoading && (!safetyTrainingRecords || safetyTrainingRecords.length === 0)">
                            <td colspan="7" class="text-center p-4 text-muted"> <!-- Updated colspan -->
                                No safety training records found.
                            </td>
                        </tr>
                        <!-- Data Rows -->
                        <tr *ngFor="let record of safetyTrainingRecords">
                            <td class="details-cell"> <!-- Basic Info -->
                                <div class="details-container">
                                    <p class="label-value"><strong>Type</strong> <span class="value-text">{{
                                            record?.type == 0 ? 'Classroom Training' :'Saksham Training' }}</span></p>
                                    <p class="label-value"><strong>Month</strong> <span class="value-text">{{
                                            record.date | date:'MMM yyyy' }}</span></p>
                                    <p class="label-value"><strong>Facility</strong> <span class="value-text">{{
                                            record?.plantType?.title ? record?.plantType?.title:'N/A' }}</span></p>
                                    <p class="label-value"><strong>Cluster</strong> <span class="value-text">{{
                                            record.cluster?.title ? record.cluster?.title :'N/A' }}</span></p>
                                    <!-- <p class="label-value"><strong>Company</strong> <span class="value-text">{{
                                            record.companyName ? record.companyName :'N/A' }}</span></p> -->
                                    <p class="label-value"><strong>Plant</strong> <span class="value-text">{{
                                            record?.plant?.name ? record.plant?.name :'N/A' }}</span></p>
                                </div>
                            </td>
                            <td class="details-cell"> <!-- Training Details -->
                                <div class="details-container">
                                    <p class="label-value"><strong>Date</strong> <span class="value-text">{{ record.date
                                            | date:'dd/MM/yyyy' }}</span></p>

                                    <p class="label-value"><strong>Faculty Type</strong> <span class="value-text">{{
                                            record.trainingType == 0 ? 'Internal' : 'External' }}</span></p>
                                    <!-- <p *ngIf="record.type == 0" class="label-value"><strong>Faculty</strong> <span class="value-text">{{
                                                                            record.facultyName }}</span></p> -->
                                  <span *ngIf="record.type == 0" class="label-2" style="border-bottom:1px dotted #ddd !important;">
                                        <p class="label-value" style="border-bottom:none !important;">
                                            <strong >Faculty</strong>
                                        </p>
                                        <span class="text-overflow" [matTooltip]="record.facultyName">{{ record.facultyName }}</span>
                                    </span>
                                    <p class="label-value" ><strong>Duration</strong> <span class="value-text">{{
                                            record.trainingProgDuration }}</span></p>
                                    <span *ngIf="record.type == 0" class="label-2" style="border-bottom:1px dotted #ddd !important;">
                                        <p class="label-value" style="border-bottom:none !important;">
                                            <strong >Topic</strong>
                                        </p>
                                        <span  class="text-overflow" [matTooltip]="record.topic">{{ record.topic }}</span>
                                    </span>
                                    <span class="label-2" style="border-bottom:1px dotted #ddd !important;">
                                     <p class="label-value" style="border-bottom:none !important;">
                                            <strong >Created By</strong>
                                        </p>
                                        <span class="text-overflow" [matTooltip]="record.admin?.email">{{ record?.adminId ?  record.admin?.firstName + ' ' + record.admin?.lastName  :'N/A'}}</span>
                                    </span>
                                       <span class="label-2" >
                                     <p class="label-value" style="border-bottom:none !important;">
                                            <strong >Updated By</strong>
                                        </p>
                                        <span class="text-overflow" [matTooltip]="record.lastUpdatedByAdmin?.email">{{ record?.lastUpdatedByAdminId ?  record.lastUpdatedByAdmin?.firstName + ' ' + record.lastUpdatedByAdmin?.lastName  :'N/A'}}</span>
                                    </span>
                                    <!-- <p *ngIf="record.type == 0" class="label-value"><strong>Topic</strong> <span class="value-text">{{
                                                                            record.topic }}</span></p> -->
                                </div>
                            </td>
                            <td class="details-cell-1"> <!-- Company Participants -->
                                <div class="details-container">
                                    <p class="label-value"><strong>Male (Mgmt)</strong> <span class="value-text">{{
                                            record.companyMaleManagement }}</span></p>
                                    <p class="label-value"><strong>Male (SFA)</strong> <span class="value-text">{{
                                            record.companyMaleSFA }}</span></p>
                                    <p class="label-value"><strong>Total Male</strong> <span class="value-text">{{
                                            record.totalCompanyMale }}</span></p>
                                    <p class="label-value"><strong>Female (Mgmt)</strong> <span class="value-text">{{
                                            record.companyFemaleManagement }}</span></p>
                                    <p class="label-value"><strong>Female (SFA)</strong> <span class="value-text">{{
                                            record.companyFemaleSFA }}</span></p>
                                    <p class="label-value"><strong>Total Female</strong> <span class="value-text">{{
                                            record.totalCompanyFemale }}</span></p>
                                    <p class="label-value"><strong>Total Company</strong> <span class="value-text">{{
                                            record.totalNoOfCompanyEmpParticipant }}</span></p>
                                    <p class="label-value"><strong>Male (Contractor)</strong> <span class="value-text">{{
                                            record.contractMale }}</span></p>
                                    <p class="label-value"><strong>Female (Contractor)</strong> <span class="value-text">{{
                                            record.contractFemale }}</span></p>
                                    <p class="label-value"><strong>Total Contractor</strong> <span class="value-text">{{
                                            record.totalContractEmpParticipant }}</span></p>
                                </div>
                            </td>

                            <td class="details-cell-1"> <!-- Totals & Manhours -->
                                <div class="details-container">
                                    <p class="label-value"><strong>Total Participants</strong> <span
                                            class="value-text">{{ record.totalParticpant }}</span></p>
                                    <p class="label-value"><strong>Company Hours</strong> <span class="value-text">{{
                                            record.companyEmpManhour }}</span></p>
                                    <p class="label-value"><strong>Contractor Hours</strong> <span class="value-text">{{
                                            record.contractEmpManhour }}</span></p>
                                    <p class="label-value"><strong>Total Hours</strong> <span class="value-text">{{
                                            record.totalManHour }}</span></p>
                                </div>
                            </td>
                            <!-- <td class="details-cell-1">
                                <div class="details-container">
                                    <p class="label-value"><strong>Male</strong> <span class="value-text">{{
                                            record.contractMale }}</span></p>
                                    <p class="label-value"><strong>Female</strong> <span class="value-text">{{
                                            record.contractFemale }}</span></p>
                                    <p class="label-value"><strong>Total</strong> <span class="value-text">{{
                                            record.totalContractEmpParticipant }}</span></p>
                                </div>
                            </td> -->
                            <td class="text-center align-middle details-cell-2"> <!-- Feedback -->
                                <p  class="align-middle remark-text" [matTooltip]="record.remark">   {{ record.remark }}</p>
                            </td>
                            <td class="text-center align-middle"> <!-- Actions -->
                                <!-- Edit Button -->
                                <button type="button" class="btn btn-warning btn-sm btn-edit" title="Edit Record"
                                    (click)="openEditModal(record)">
                                    <i class="bi bi-pencil-fill"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <!-- Pagination -->
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- ======================= -->
<!-- Offcanvas Components    -->
<!-- ======================= -->

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Safety Training Records'" [width]="'512px'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                <div class="col-md-6" *ngIf="roleType =='superadmin'">
                    <label for="filterFacility" class="form-label">Facility</label>
                    <select id="filterFacility" class="form-select" [(ngModel)]="filters.plantTypeId" (change)="onFacilityChange(filters.plantTypeId)" name="facilityFilter">
                        <option [ngValue]="null">All Facilities</option>
                        <option *ngFor="let facility of availableFacilities" [ngValue]="facility.id">{{ facility.title
                            }}</option>
                    </select>
                </div>
                <!-- <div class="col-md-6">
                    <label for="filterCluster" class="form-label">Cluster</label>
                    <select id="filterCluster" class="form-select" [(ngModel)]="filters.clusterId" name="clusterFilter">
                        <option [ngValue]="null">All Clusters</option>
                        <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}
                        </option>
                    </select>
                </div> -->
                <!-- <div class="col-md-6">
                    <label for="filterCompanyName" class="form-label">Name of Company</label>
                    <select id="filterCompanyName" class="form-select" [(ngModel)]="filters.companyName"
                        name="companyNameFilter">
                        <option [ngValue]="null">All Companies</option>
                        <option *ngFor="let company of availableCompanies" [ngValue]="company">{{ company }}</option>
                    </select>
                </div> -->
                <div class="col-md-6">
                    <label for="filterPlant" class="form-label">Plant</label>
                    <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantFilter" (change)="onPlantSelect(filters.plantId)">
                        <option *ngIf=" roleType != 'plantadmin'" [ngValue]="null">All Plants</option>
                        <option *ngIf=" roleType == 'plantadmin'" [ngValue]="null">Select Plants</option>
                        <option *ngFor="let plant of filteredPlants" [ngValue]="plant.id" >{{ plant.name }}</option>
                    </select>
                </div>
                    <div class="col-6">
                    <label for="filterPlants" class="form-label">Select Month(s)</label>
                    <ng-select
                        [items]="filteredMonths"
                        bindValue="id"
                        bindLabel="name"
                        [multiple]="true"
                        [closeOnSelect]="false"
                        [clearable]="false"
                        [(ngModel)]="filters.months"
                        (change)="updateSelectAllState()"
                        name="monthFilter"
                        placeholder="Select Month(s)">
                        <ng-template ng-header-tmp>
                            <div class="form-check mb-1 ms-2">
                                <input class="form-check-input" type="checkbox"
                                       id="selectAllPlantsCheckbox"
                                       [checked]="isAllMonthsSelected"
                                       (change)="toggleSelectAllPlants($event)">
                                <label class="form-check-label small" for="selectAllPlantsCheckbox">
                                    Select All / Deselect All
                                </label>
                            </div>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{ item.name }}
                        </ng-template>
                    </ng-select>
                </div>
                 <div class="col-6" >
                        <label class="form-label" for="createYear">Year</label>
                        <select id="createYear" class="form-select p-2" (ngModelChange)="onYearChange($event)"
                            [(ngModel)]="year" name="selectedYear" required
                            >
                            <option [ngValue]="null" disabled> Year </option>
                            <option *ngFor="let year of uniqueYearsForFilter" [value]="year">{{ year }}</option>
                        </select>
                    </div>

            </div>
            <div class="row p-3 mt-4 justify-content-center">
                    <button type="submit" class="btn adani-btn col-4 m-1" (click)="applyFilters()">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary col-4 m-1" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
        </form>
    </div>
</app-offcanvas>

<!-- Create Offcanvas -->
<app-offcanvas [title]="'Add New Safety Training Record'" [width]="'750px'" *ngIf="isCreateModalOpen"
    (onClickCross)="closeCreateModal()">
    <div class="create-container p-3">
        <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">
                <!-- Form fields -->
                <div class="col-md-4">
                    <label class="form-label" for="type">Training Type <span class="text-danger">*</span></label>
                    <select id="type" class="form-select" [(ngModel)]="newRecordData.type" name="type" required
                        (ngModelChange)="onTrainingTypeChange(newRecordData.type)">
                        <option [ngValue]="null" disabled>-- Select Training Type --</option>
                        <option [ngValue]="0">Classroom Training</option>
                        <option [ngValue]="1">Saksham Training </option>

                    </select>
                    <div *ngIf="createForm.controls['type']?.invalid && (createForm.controls['type']?.dirty || createForm.controls['type']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Training Type is Required</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="date">Date of Training Program <span
                            class="text-danger">*</span></label>
                    <input type="date" id="date" class="form-control" [(ngModel)]="newRecordData.date" name="date"
                        [min]="minDate" [max]="maxDate" required #dateField="ngModel"
                        (ngModelChange)="validateDate($event)">
                    <div *ngIf="(dateField.invalid && (dateField.dirty || dateField.touched || createForm.submitted))"
                        class="text-danger small mt-1">
                        <div *ngIf="dateField.errors?.['required']">Date of Training Program is required.</div>

                    </div >
                    <div  class="text-danger small mt-1" *ngIf="dateErrorMessage"> {{ dateErrorMessage }}</div>

                </div>
                <!-- <div class="col-md-4">
                    <label class="form-label" for="createMonth">Month <span class="text-danger">*</span></label>
                    <input type="date" id="createMonth" class="form-control" [(ngModel)]="newRecordData.month" name="month"
                        required>
                    <div *ngIf="createForm.controls['month']?.invalid && (createForm.controls['month']?.dirty || createForm.controls['month']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Month is Required</div>
                </div> -->
                <div class="col-md-4"  *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="facility">Facility <span class="text-danger">*</span></label>
                    <select id="facility" class="form-select" [(ngModel)]="newRecordData.plantTypeId" name="plantTypeId" required (change)="onFacilityChange(newRecordData.plantTypeId)">
                        <option [ngValue]="null" disabled>-- Select Facility --</option>
                        <option *ngFor="let facility of availableFacilities" [ngValue]="facility.id">{{ facility.title
                            }}
                        </option>
                    </select>
                    <div *ngIf="createForm.controls['plantTypeId']?.invalid && (createForm.controls['plantTypeId']?.dirty || createForm.controls['plantTypeId']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Facility is Required</div>
                </div>
                <div class="col-md-4"  *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="plantId">Plant <span class="text-danger">*</span></label>
                    <select id="plantId" class="form-select" [(ngModel)]="newRecordData.plantId" name="plantId" (change)="setClusterOnPlant(newRecordData.plantId)"
                        required [disabled]="newRecordData.plantTypeId === null || newRecordData.plantTypeId === undefined">
                        <option [ngValue]="null" disabled>-- Select Plant --</option>
                        <option *ngFor="let plant of filteredPlants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                    <div *ngIf="createForm.controls['plantId']?.invalid && (createForm.controls['plantId']?.dirty || createForm.controls['plantId']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Plant is Required</div>
                </div>
                <div class="col-md-4" *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="clusterId">Cluster <span class="text-danger">*</span></label>
                    <select id="clusterId" class="form-select"  disabled  [(ngModel)]="newRecordData.clusterId" name="clusterId"
                        required>
                        <option [ngValue]="null" disabled>-- Select Cluster --</option>
                        <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}
                        </option>
                    </select>
                    <div *ngIf="createForm.controls['clusterId']?.invalid && (createForm.controls['clusterId']?.dirty || createForm.controls['clusterId']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Cluster is Required</div>
                </div>
                <!-- <div class="col-md-4"  *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="plantTypeId">Name of Company <span class="text-danger">*</span></label>
                    <select id="plantTypeId" class="form-select" [(ngModel)]="newRecordData.companyName" name="companyName"
                        required>
                        <option [ngValue]="null" disabled>-- Select Company --</option>
                        <option *ngFor="let company of availableCompanies" [ngValue]="company">{{ company }}
                        </option>
                    </select>
                    <div *ngIf="createForm.controls['companyName']?.invalid && (createForm.controls['companyName']?.dirty || createForm.controls['companyName']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Company is Required</div>
                </div> -->


                <div class="col-md-4">
                    <label class="form-label" for="trainingType">Faculty Type <span class="text-danger">*</span></label>
                    <select id="createFacility" class="form-select" [(ngModel)]="newRecordData.trainingType"
                        name="trainingType" required>
                        <option [ngValue]="null" disabled>-- Select Faculty --</option>
                        <option [ngValue]="0">Internal</option>
                        <option [ngValue]="1" *ngIf="newRecordData.type == 0">External </option>
                    </select>
                    <div *ngIf="createForm.controls['trainingType']?.invalid && (createForm.controls['trainingType']?.dirty || createForm.controls['trainingType']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Faculty Type Required</div>
                </div>
                <div *ngIf="newRecordData.type == 0 " class="col-md-4">
                    <label class="form-label" for="topic">Training Topic <span class="text-danger">*</span></label>
                    <input type="text" id="topic" class="form-control" [(ngModel)]="newRecordData.topic" name="topic"
                        required pattern="^(?=(?:.*[A-Za-z]){5,})[A-Za-z0-9 .,!?'()\-]{5,200}$" #topic="ngModel"
                        minlength="3" maxlength="200">
                    <div *ngIf="(topic.invalid && (topic.dirty || topic.touched || createForm.submitted))"
                        class="text-danger small mt-1">
                        <div *ngIf="topic.errors?.['required']">Training Topic is required.</div>
                        <span *ngIf="topic.errors?.['pattern']">Must contain at least 5 letters and max 200 letters and only valid
                            characters</span>
                        <div *ngIf="topic.errors?.['minlength']">Enter Valid Topic</div>
                    </div>
                </div>

                <div *ngIf="newRecordData.type == 0 " class="col-md-4">
                    <label class="form-label" for="facultyName">Name of the Faculty <span
                            class="text-danger">*</span></label>
                    <input type="text" id="facultyName" class="form-control" [(ngModel)]="newRecordData.facultyName"
                        name="facultyName" required pattern="^(?=(?:.*[a-zA-Z]){3,})[a-zA-Z\s]+$" #facultyName="ngModel" minlength="3"
                        maxlength="50">
                    <div *ngIf="facultyName.invalid && (facultyName.dirty || facultyName.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        <div *ngIf="facultyName.errors?.['required']">Name of the Faculty is required.</div>
                        <div *ngIf="facultyName.errors?.['minlength'] || facultyName.errors?.['maxlength'] || facultyName.errors?.['pattern']">
                             Name must contain at least 3 letters (spaces allowed)</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyMaleManagement">No. of Company Male Participants(Management)
                        <span class="text-danger"> *</span></label>
                    <input type="number" id="companyMaleManagement" class="form-control"
                        [(ngModel)]="newRecordData.companyMaleManagement" name="companyMaleManagement" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['companyMaleManagement']?.errors?.['required'] && (createForm.controls['companyMaleManagement']?.dirty || createForm.controls['companyMaleManagement']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Company Male Participants(Management) is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['companyMaleManagement']?.errors?.['min']">Minimum value must be 0.</div>


                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyMaleSFA">No. of Company Male Participants(SFA) <span
                            class="text-danger">*</span></label>
                    <input type="number" id="companyMaleSFA" class="form-control"
                        [(ngModel)]="newRecordData.companyMaleSFA" name="companyMaleSFA" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['companyMaleSFA']?.errors?.['required'] && (createForm.controls['companyMaleSFA']?.dirty || createForm.controls['companyMaleSFA']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Company Male Participants(SFA) is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['companyMaleSFA']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalCompanyMale">Total No. of Company Male Employee <span
                            class="text-danger">*</span></label>
                    <input type="number" id="totalCompanyMale" class="form-control"
                        [(ngModel)]="newRecordData.totalCompanyMale" name="totalCompanyMale" required min="0" readonly
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['totalCompanyMale']?.errors?.['required'] && (createForm.controls['totalCompanyMale']?.dirty || createForm.controls['totalCompanyMale']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Male Employee is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalCompanyMale']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyFemaleManagement">No. of Company Female
                        Participants(Management)<span class="text-danger"> *</span></label>
                    <input type="number" id="companyFemaleManagement" class="form-control"
                        [(ngModel)]="newRecordData.companyFemaleManagement" name="companyFemaleManagement" required
                        min="0" (input)="onBreakdownFieldChange()"(keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['companyFemaleManagement']?.errors?.['required'] && (createForm.controls['companyFemaleManagement']?.dirty || createForm.controls['companyFemaleManagement']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Company Female Participants (Management) is Required</div>
                    <div   class="text-danger small mt-1" *ngIf="createForm.controls['companyFemaleManagement']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyFemaleSFA">No. of Company Female Participants(SFA) <span
                            class="text-danger">*</span></label>
                    <input type="number" id="companyFemaleSFA" class="form-control"
                        [(ngModel)]="newRecordData.companyFemaleSFA" name="companyFemaleSFA" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['companyFemaleSFA']?.errors?.['required'] && (createForm.controls['companyFemaleSFA']?.dirty || createForm.controls['companyFemaleSFA']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Company Female Participants(SFA) is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['companyFemaleSFA']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalCompanyFemale">Total No. of Company Female Employee
                        <span class="text-danger">*</span></label>
                    <input type="number" id="totalCompanyFemale" class="form-control"
                        [(ngModel)]="newRecordData.totalCompanyFemale" name="totalCompanyFemale" required min="0"
                        (input)="onBreakdownFieldChange()" readonly (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['totalCompanyFemale']?.errors?.['required'] && (createForm.controls['totalCompanyFemale']?.dirty || createForm.controls['totalCompanyFemale']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Female Employee is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalCompanyFemale']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalNoOfCompanyEmpParticipant">Total No. of Company Employee
                        Participants <span class="text-danger">*</span></label>
                    <input type="number" id="totalNoOfCompanyEmpParticipant" class="form-control"
                        [(ngModel)]="newRecordData.totalNoOfCompanyEmpParticipant" name="totalNoOfCompanyEmpParticipant"
                        required min="0" readonly (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['totalNoOfCompanyEmpParticipant']?.errors?.['required'] && (createForm.controls['totalNoOfCompanyEmpParticipant']?.dirty || createForm.controls['totalNoOfCompanyEmpParticipant']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Employee Participants is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalNoOfCompanyEmpParticipant']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="contractMale">No. of Contractor Male Employees <span
                            class="text-danger">*</span></label>
                    <input type="number" id="contractMale" class="form-control" [(ngModel)]="newRecordData.contractMale"
                        name="contractMale" required min="0" (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['contractMale']?.errors?.['required'] && (createForm.controls['contractMale']?.dirty || createForm.controls['contractMale']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Contractor Male Employees is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['contractMale']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="contractFemale">No. of Contractor Female Employees <span
                            class="text-danger">*</span></label>
                    <input type="number" id="contractFemale" class="form-control"
                        [(ngModel)]="newRecordData.contractFemale" name="contractFemale" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['contractFemale']?.errors?.['required'] && (createForm.controls['contractFemale']?.dirty || createForm.controls['contractFemale']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">No. of Contractor Female Employees is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['contractFemale']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-8">
                    <label class="form-label" for="totalContractEmpParticipant">Total No. of Contractor Employee
                        Participants <span class="text-danger">*</span></label>
                    <input type="number" id="totalContractEmpParticipant" readonly class="form-control"
                        [(ngModel)]="newRecordData.totalContractEmpParticipant" name="totalContractEmpParticipant"
                        required min="0" (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="createForm.controls['totalContractEmpParticipant']?.errors?.['required'] && (createForm.controls['totalContractEmpParticipant']?.dirty || createForm.controls['totalContractEmpParticipant']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total No. of Contractor Employee is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalContractEmpParticipant']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalParticpant">Total Participants <span
                            class="text-danger">*</span></label>
                    <input type="number" id="totalParticpant" class="form-control" (keypress)="allowOnlyInteger($event)"
                        [(ngModel)]="newRecordData.totalParticpant" name="totalParticpant" required min="1" readonly>
                    <div *ngIf="createForm.controls['totalParticpant']?.errors?.['required'] && (createForm.controls['totalParticpant']?.dirty || createForm.controls['totalParticpant']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total Participants is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalParticpant']?.errors?.['min']">Minimum value must be 1.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="trainingProgDuration">Duration of Training Program <span
                            class="text-danger">*</span></label>
                    <input type="number" id="trainingProgDuration" class="form-control" [min]="1"
                        (input)="onBreakdownFieldChange()" [(ngModel)]="newRecordData.trainingProgDuration"
                        name="trainingProgDuration" required>
                    <div *ngIf="createForm.controls['trainingProgDuration']?.errors?.['required'] && (createForm.controls['trainingProgDuration']?.dirty || createForm.controls['trainingProgDuration']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Duration of Training Program is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['trainingProgDuration']?.errors?.['min']">Minimum value must be 1</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="companyEmpManhour">Total Company Employees Manhours <span
                            class="text-danger">*</span></label>
                    <input type="number" id="companyEmpManhour" class="form-control" readonly
                        [(ngModel)]="newRecordData.companyEmpManhour" name="companyEmpManhour" required min="0"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['companyEmpManhour']?.errors?.['required'] && (createForm.controls['companyEmpManhour']?.dirty || createForm.controls['companyEmpManhour']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total Company Employees Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['companyEmpManhour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="contractEmpManhour">Total Contractor Employees Manhours <span
                            class="text-danger">*</span></label>
                    <input type="number" id="contractEmpManhour" class="form-control" readonly
                        [(ngModel)]="newRecordData.contractEmpManhour" name="contractEmpManhour" required min="0"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="createForm.controls['contractEmpManhour']?.errors?.['required'] && (createForm.controls['contractEmpManhour']?.dirty || createForm.controls['contractEmpManhour']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total Contractor Employees Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['contractEmpManhour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="totalManHour">Total Manhours <span
                            class="text-danger">*</span></label>
                    <input type="number" id="totalManHour" class="form-control" [(ngModel)]="newRecordData.totalManHour"
                        name="totalManHour" required min="0" readonly>
                    <div *ngIf="createForm.controls['totalManHour']?.errors?.['required'] && (createForm.controls['totalManHour']?.dirty || createForm.controls['totalManHour']?.touched || createForm.submitted)"
                        class="text-danger small mt-1">Total Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="createForm.controls['totalManHour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-12">
                    <label class="form-label" for="remark">Remarks / Feedback</label>
                    <textarea id="remark" class="form-control" [(ngModel)]="newRecordData.remark" name="remark"
                        minlength="15" maxlength="300"  pattern="^(?=(?:.*[a-zA-Z0-9]){15,})[a-zA-Z0-9\s.,!?'\()\-]+$" #remark="ngModel"
                        ></textarea>
                    <!-- <div *ngIf="remark.errors?.['required'] && (remark.dirty || remark.touched || createForm.submitted)"
                        class="text-danger small mt-1">
                        Remarks / Feedback is required.
                    </div> -->
                    <div class="text-danger small mt-1" *ngIf="remark.errors?.['pattern']">
                        Only letters, numbers, spaces, and basic punctuation allowed. Minimum 15 valid characters required.
                    </div>
                    </div>

                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="createForm?.invalid || createLoading">
                        <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Add Record</span>
                        <span *ngIf="createLoading">
                            <span class="spinner-border spinner-border-sm me-1"></span>
                            Adding...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- Edit Offcanvas -->
<app-offcanvas [title]="'Edit Safety Training Record'" [width]="'750px'" *ngIf="isEditModalOpen && editingRecordData"
    (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <form #editForm="ngForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">
                <!-- Form fields -->
                <div class="col-md-4">
                    <label class="form-label" for="createFacility">Training Type <span
                            class="text-danger">*</span></label>
                    <select disabled id="createFacility" class="form-select" [(ngModel)]="editingRecordData.type"
                        name="type" required (ngModelChange)="onTrainingTypeChange(editingRecordData.type)">
                        <option [ngValue]="null" disabled>-- Select Training Type --</option>
                        <option [ngValue]="0">Classroom Training</option>
                        <option [ngValue]="1">Saksham Training </option>

                    </select>
                    <div *ngIf="editForm.controls['type']?.invalid && (editForm.controls['type']?.dirty || editForm.controls['type']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Training Type is Required</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="date">Date of Training Program <span
                            class="text-danger">*</span></label>
                    <input type="date" id="date" class="form-control" [(ngModel)]="editingRecordData.date" name="date"
                        [min]="minDate" [max]="maxDate" required #date="ngModel" (ngModelChange)="validateDate($event)">
                    <div *ngIf="(date.invalid && (date.dirty || date.touched || editForm.submitted))"
                        class="text-danger small mt-1">
                        <div *ngIf="date.errors?.['required']">Date of Training Program is required.</div>
                    </div>
                     <div  class="text-danger small mt-1" *ngIf="dateErrorMessage"> {{ dateErrorMessage }}</div>
                </div>

                <!-- <div class="col-md-4">
                    <label class="form-label" for="month">Month <span class="text-danger">*</span></label>
                    <input type="date" id="month" class="form-control" [(ngModel)]="editingRecordData!.month" name="month"
                        required>
                        <div *ngIf="editForm.controls['month']?.invalid && (editForm.controls['month']?.dirty || editForm.controls['month']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Month is Required</div>
                </div> -->
                <div class="col-md-4" *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="editFacility">Facility <span class="text-danger">*</span></label>
                    <select id="editFacility" class="form-select" [(ngModel)]="editingRecordData!.plantTypeId" name="plantTypeId" (change)="onFacilityChange(editingRecordData!.plantTypeId)"
                        required>
                        <option [ngValue]="null" disabled>-- Select Facility --</option>
                        <option *ngFor="let facility of availableFacilities" [ngValue]="facility.id">{{ facility.title
                            }}
                        </option>
                    </select>
                    <div *ngIf="editForm.controls['plantTypeId']?.invalid && (editForm.controls['plantTypeId']?.dirty || editForm.controls['plantTypeId']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Facility is Required</div>
                </div>
                  <div class="col-md-4" *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="editPlant">Plant <span class="text-danger">*</span></label>
                    <select id="editPlant" class="form-select" [(ngModel)]="editingRecordData!.plantId" name="plantId" (change)="setClusterOnPlant(editingRecordData!.plantId)"
                        required>
                        <option [ngValue]="null" disabled>-- Select Plant --</option>
                        <option *ngFor="let plant of filteredPlants" [ngValue]="plant.id">{{ plant.name }}</option>
                    </select>
                    <div *ngIf="editForm.controls['plantId']?.invalid && (editForm.controls['plantId']?.dirty || editForm.controls['plantId']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Plant is Required</div>
                </div>
                <div class="col-md-4" *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="editCluster">Cluster <span class="text-danger">*</span></label>
                    <select id="editCluster" class="form-select" disabled [(ngModel)]="editingRecordData!.clusterId"
                        name="clusterId" required>
                        <option [ngValue]="null" disabled>-- Select Cluster --</option>
                        <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}
                        </option>
                    </select>
                    <div *ngIf="editForm.controls['clusterId']?.invalid && (editForm.controls['clusterId']?.dirty || editForm.controls['clusterId']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Cluster is Required</div>
                </div>
                <!-- <div class="col-md-4" *ngIf="roleType !=='plantadmin'">
                    <label class="form-label" for="editCompanyName">Name of Company <span
                            class="text-danger">*</span></label>
                    <select id="editCompanyName" class="form-select" [(ngModel)]="editingRecordData!.companyName"
                        name="companyName" required>
                        <option [ngValue]="null" disabled>-- Select Company --</option>
                        <option *ngFor="let company of availableCompanies" [ngValue]="company">{{ company }}</option>
                    </select>
                    <div *ngIf="editForm.controls['companyName']?.invalid && (editForm.controls['companyName']?.dirty || editForm.controls['companyName']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Company is Required</div>
                </div> -->


                <div class="col-md-4">
                    <label class="form-label" for="trainingType">Faculty Type<span class="text-danger">*</span></label>
                    <select id="createFacility" class="form-select" [(ngModel)]="editingRecordData.trainingType"
                        name="trainingType" required>
                        <option [ngValue]="null" disabled>-- Select Faculty Type--</option>
                        <option [ngValue]="0">Internal</option>
                        <option [ngValue]="1" *ngIf="editingRecordData.type == 0">External </option>
                    </select>
                    <div *ngIf="editForm.controls['trainingType']?.invalid && (editForm.controls['trainingType']?.dirty || editForm.controls['trainingType']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Faculty Type Required</div>
                </div>
                <div *ngIf="editingRecordData.type == 0" class="col-md-4">
                    <label class="form-label" for="topic">Training Topic <span class="text-danger">*</span></label>
                    <input type="text" id="topic" class="form-control" [(ngModel)]="newRecordData.topic" name="topic"
                        required pattern="^(?=(?:.*[A-Za-z]){5,})[A-Za-z0-9 .,!?'()\-]{5,200}$" #topic="ngModel"
                        minlength="3" maxlength="200">
                    <div *ngIf="(topic.invalid && (topic.dirty || topic.touched || editForm.submitted))"
                        class="text-danger small mt-1">
                        <div *ngIf="topic.errors?.['required']">Training Topic is required.</div>
                        <span *ngIf="topic.errors?.['pattern']">Must contain at least 5 letters letters and max 200 and only valid
                            characters</span>
                        <div *ngIf="topic.errors?.['minlength']">Enter Valid Topic</div>
                    </div>
                </div>

                <div *ngIf="editingRecordData.type == 0 " class="col-md-4">
                    <label class="form-label" for="facultyName">Name of the Faculty <span
                            class="text-danger">*</span></label>
                    <input type="text" id="facultyName" class="form-control" [(ngModel)]="editingRecordData.facultyName"
                        name="facultyName" required pattern="^(?=(?:.*[a-zA-Z]){3,})[a-zA-Z\s]+$" #facultyName="ngModel" minlength="3"
                        maxlength="50">
                    <div *ngIf="facultyName.invalid && (facultyName.dirty || facultyName.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        <div *ngIf="facultyName.errors?.['required']">Name of the Faculty is required.</div>
                        <div *ngIf="facultyName.errors?.['minlength'] || facultyName.errors?.['maxlength'] || facultyName.errors?.['pattern']">
                             Name must contain at least 3 letters (spaces allowed)</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyMaleManagement">No. of Company Male Participants(Management)
                        <span class="text-danger">*</span></label>
                    <input type="number" id="companyMaleManagement" class="form-control"
                        [(ngModel)]="editingRecordData!.companyMaleManagement" name="companyMaleManagement" required
                        min="0" (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                     <div class="text-danger small mt-1" *ngIf="editForm.controls['companyMaleManagement']?.errors?.['min']">Minimum value must be 0.</div>
                    <div *ngIf="editForm.controls['companyMaleManagement']?.errors?.['required'] && (editForm.controls['companyMaleManagement']?.dirty || editForm.controls['companyMaleManagement']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Company Male Participants(Management) is Required</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyMaleSFA">No. of Company Male Participants (SFA)
                        <span class="text-danger">*</span></label>
                    <input type="number" id="companyMaleSFA" class="form-control"
                        [(ngModel)]="editingRecordData!.companyMaleSFA" name="companyMaleSFA" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['companyMaleSFA']?.errors?.['required'] && (editForm.controls['companyMaleSFA']?.dirty || editForm.controls['companyMaleSFA']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Company Male Participants(SFA) is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['companyMaleSFA']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalCompanyMale">Total No. of Company Male Employee
                        <span class="text-danger">*</span></label>
                    <input type="number" id="totalCompanyMale" class="form-control"
                        [(ngModel)]="editingRecordData!.totalCompanyMale" name="totalCompanyMale" required min="0"
                        readonly (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['totalCompanyMale']?.errors?.['required'] && (editForm.controls['totalCompanyMale']?.dirty || editForm.controls['totalCompanyMale']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Male Employee is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['totalCompanyMale']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyFemaleManagement">No. of Company Female
                        Participants(Management) <span class="text-danger">*</span></label>
                    <input type="number" id="companyFemaleManagement" class="form-control"
                        [(ngModel)]="editingRecordData!.companyFemaleManagement" name="companyFemaleManagement" required
                        min="0" (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['companyFemaleManagement']?.errors?.['required'] && (editForm.controls['companyFemaleManagement']?.dirty || editForm.controls['companyFemaleManagement']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Company Female Participants (Management) is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['companyFemaleManagement']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="companyFemaleSFA">No. of Company Female Participants
                        (SFA) <span class="text-danger">*</span></label>
                    <input type="number" id="companyFemaleSFA" class="form-control"
                        [(ngModel)]="editingRecordData!.companyFemaleSFA" name="companyFemaleSFA" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['companyFemaleSFA']?.errors?.['required'] && (editForm.controls['companyFemaleSFA']?.dirty || editForm.controls['companyFemaleSFA']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Company Female Participants(SFA) is Required</div>
                    <div   class="text-danger small mt-1"*ngIf="editForm.controls['companyFemaleSFA']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalCompanyFemale">Total No. of Company Female Employee
                        <span class="text-danger">*</span></label>
                    <input type="number" id="totalCompanyFemale" class="form-control" (input)="onBreakdownFieldChange()"
                        [(ngModel)]="editingRecordData!.totalCompanyFemale" name="totalCompanyFemale" required min="0"
                        readonly (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['totalCompanyFemale']?.errors?.['required'] && (editForm.controls['totalCompanyFemale']?.dirty || editForm.controls['totalCompanyFemale']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Female Employee is Required</div>
                     <div  class="text-danger small mt-1" *ngIf="editForm.controls['totalCompanyFemale']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalNoOfCompanyEmpParticipant">Total No. of Company Employee
                        Participants <span class="text-danger">*</span></label>
                    <input type="number" id="totalNoOfCompanyEmpParticipant" class="form-control"
                        [(ngModel)]="editingRecordData!.totalNoOfCompanyEmpParticipant"
                        name="totalNoOfCompanyEmpParticipant" required min="0" readonly
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['totalNoOfCompanyEmpParticipant']?.errors?.['required'] && (editForm.controls['totalNoOfCompanyEmpParticipant']?.dirty || editForm.controls['totalNoOfCompanyEmpParticipant']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total No. of Company Employee Participants is Required</div>
                     <div  class="text-danger small mt-1"*ngIf="editForm.controls['totalNoOfCompanyEmpParticipant']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="contractMale">No. of Contractor Male Employees <span
                            class="text-danger">*</span></label>
                    <input type="number" id="contractMale" class="form-control"
                        [(ngModel)]="editingRecordData!.contractMale" name="contractMale" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['contractMale']?.errors?.['required'] && (editForm.controls['contractMale']?.dirty || editForm.controls['contractMale']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Contractor Male Employees is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['contractMale']?.errors?.['min']">Minimum value must be 0.</div>

                </div>
                <div class="col-md-4">
                    <label class="form-label" for="contractFemale">No. of Contractor Female Employees
                        <span class="text-danger">*</span></label>
                    <input type="number" id="contractFemale" class="form-control"
                        [(ngModel)]="editingRecordData!.contractFemale" name="contractFemale" required min="0"
                        (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['contractFemale']?.errors?.['required'] && (editForm.controls['contractFemale']?.dirty || editForm.controls['contractFemale']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">No. of Contractor Female Employees is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['contractFemale']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-8">
                    <label class="form-label" for="totalContractEmpParticipant">Total No. of Contractor Employee
                        Participants
                        <span class="text-danger">*</span></label>
                    <input type="number" id="totalContractEmpParticipant" class="form-control" readonly
                        [(ngModel)]="editingRecordData!.totalContractEmpParticipant" name="totalContractEmpParticipant"
                        required min="0" (input)="onBreakdownFieldChange()" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['totalContractEmpParticipant']?.errors?.['required'] && (editForm.controls['totalContractEmpParticipant']?.dirty || editForm.controls['totalContractEmpParticipant']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total No. of Contractor Employee is Required</div>
                    <div  class="text-danger small mt-1"*ngIf="editForm.controls['totalContractEmpParticipant']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="totalParticpant">Total Participants <span
                            class="text-danger">*</span></label>
                    <input type="number" id="totalParticpant" class="form-control" readOnly
                        [(ngModel)]="editingRecordData!.totalParticpant" name="totalParticpant" required min="1" (keypress)="allowOnlyInteger($event)">
                    <div *ngIf="editForm.controls['totalParticpant']?.errors?.['required'] && (editForm.controls['totalParticpant']?.dirty || editForm.controls['totalParticpant']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total Participants is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['totalParticpant']?.errors?.['min']">Minimum value must be 1.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="trainingProgDuration">Duration of Training Program <span
                            class="text-danger">*</span></label>
                    <input type="number" id="trainingProgDuration" class="form-control" [min]="1"
                        [(ngModel)]="editingRecordData!.trainingProgDuration" name="trainingProgDuration" required
                         (input)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['trainingProgDuration']?.errors?.['required'] && (editForm.controls['trainingProgDuration']?.dirty || editForm.controls['trainingProgDuration']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Duration of Training Program is Required</div>
                    <div   class="text-danger small mt-1"*ngIf="editForm.controls['trainingProgDuration']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="companyEmpManhour">Total Company Employees Manhours <span
                            class="text-danger">*</span></label>
                    <input type="number" id="companyEmpManhour" class="form-control"
                        [(ngModel)]="editingRecordData!.companyEmpManhour" name="companyEmpManhour" required min="0" readonly
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['companyEmpManhour']?.errors?.['required'] && (editForm.controls['companyEmpManhour']?.dirty || editForm.controls['companyEmpManhour']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total Company Employees Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['companyEmpManhour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="contractEmpManhour">Total Contractor Employees Manhours
                        <span class="text-danger">*</span></label>
                    <input type="number" id="contractEmpManhour" class="form-control" readonly
                        [(ngModel)]="editingRecordData!.contractEmpManhour" name="contractEmpManhour" required min="0"
                        (input)="onBreakdownFieldChange()">
                    <div *ngIf="editForm.controls['contractEmpManhour']?.errors?.['required'] && (editForm.controls['contractEmpManhour']?.dirty || editForm.controls['contractEmpManhour']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total Contractor Employees Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['contractEmpManhour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label" for="totalManHour">Total Manhours <span
                            class="text-danger">*</span></label>
                    <input type="number" id="totalManHour" class="form-control"
                        [(ngModel)]="editingRecordData!.totalManHour" name="totalManHour" required min="0" readonly>
                    <div *ngIf="editForm.controls['totalManHour']?.errors?.['required'] && (editForm.controls['totalManHour']?.dirty || editForm.controls['totalManHour']?.touched || editForm.submitted)"
                        class="text-danger small mt-1">Total Manhours is Required</div>
                    <div  class="text-danger small mt-1" *ngIf="editForm.controls['totalManHour']?.errors?.['min']">Minimum value must be 0.</div>
                </div>
                <div class="col-md-12">
                    <label class="form-label" for="remark">Remarks / Feedback </label>
                    <textarea id="remark" class="form-control" [(ngModel)]="editingRecordData!.remark" name="remark"
                        minlength="15" maxlength="300"  pattern="^(?=(?:.*[a-zA-Z0-9]){5,})[a-zA-Z0-9\s.,!?'\()\-]+$" #remark="ngModel"
                        ></textarea>
                    <!-- <div *ngIf="remark.errors?.['required'] && (remark.dirty || remark.touched || editForm.submitted)"
                        class="text-danger small mt-1">
                        Remarks / Feedback is required.
                    </div> -->
                    <div class="text-danger small mt-1" *ngIf="remark.errors?.['pattern']">
                        Only letters, numbers, spaces, and basic punctuation allowed. Minimum 5 valid characters required.
                    </div>
                    </div>
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="editForm?.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save-fill me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading">
                            <span class="spinner-border spinner-border-sm me-1"></span>
                            Saving...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
