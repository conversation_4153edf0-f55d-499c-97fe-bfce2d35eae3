<app-toast-message></app-toast-message>
<div id="oneMap" class="map-view-container">
  <div class="map-controls">
    <div class="form-group">
      <label class="form-label" for="plantId">Select Plant <span class="text-danger">*</span></label>
      <select id="filterPlant" class="form-select" [(ngModel)]="selectedPlantId"
        (ngModelChange)="onPlantSelectionChange($event)" name="plantId">
        <option [ngValue]="0">
          {{ availablePlants.length > 0 ? 'Select Plant' : 'No plants found' }}
        </option>
        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
      </select>
    </div>
    <div class="form-group">
      <label class="form-label" for="mapType">Map Type</label>
      <select id="mapType" class="form-select map-type-select" [(ngModel)]="selectedMapType"
        (ngModelChange)="onMapTypeChange()">
        <option value="circle">Circle</option>
        <option value="polygon">Polygon</option>
      </select>
    </div>
  </div>
  <div #apiMapDiv class="map-container"></div>
</div>
