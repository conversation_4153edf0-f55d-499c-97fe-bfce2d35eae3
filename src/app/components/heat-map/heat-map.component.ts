import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { QrCodeService } from '../../services/qr-code/qr-code.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { environment } from '../../enviornments/enviornments';

// Declare Google Maps types for TypeScript
declare var google: any;


interface Plant {
  id: number;
  name: string;
}

@Component({
  selector: 'app-heat-map',
  standalone: true,
  imports: [CommonModule, FormsModule, NgSelectModule, ToastMessageComponent],
  templateUrl: './heat-map.component.html',
  styleUrls: ['./heat-map.component.scss']
})
export class HeatMapComponent implements AfterViewInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  //getGoogleMapId = environment.googleMapId;
  @ViewChild('apiMapDiv') apiMapDiv!: ElementRef;
  availablePlants: Plant[] = [];
  selectedPlantId: number = 0;
  currentUserRole: string = '';
  loggedInPlantIds: number[] = [];
  selectedPlantName: any = [];
  zoomMap = 17;
  center: any = { lat: 28.5707078, lng: 77.5470732 };
  zoneColors: { [zoneId: string]: string } = {};
  colors = ['#e6194b', '#3cb44b', '#ffe119', '#4363d8', '#f58231', '#911eb4', '#46f0f0', '#fabebe'];

  // Enhanced color schemes for better visual distinction
  polygonColors = [
    { fill: '#e6194b', stroke: '#b71c1c', hover: '#f44336' },
    { fill: '#3cb44b', stroke: '#1b5e20', hover: '#4caf50' },
    { fill: '#ffe119', stroke: '#f57f17', hover: '#ffeb3b' },
    { fill: '#4363d8', stroke: '#1a237e', hover: '#3f51b5' },
    { fill: '#f58231', stroke: '#e65100', hover: '#ff9800' },
    { fill: '#911eb4', stroke: '#4a148c', hover: '#9c27b0' },
    { fill: '#46f0f0', stroke: '#006064', hover: '#00bcd4' },
    { fill: '#fabebe', stroke: '#880e4f', hover: '#e91e63' }
  ];

  availableQrCodes: any[] = [];
  zoneWiseQrCodes: { [zoneName: string]: any[] } = {};
  private map: any = null; // Reference to the map instance

  private infoWindow: any = undefined;
  private currentlyOpenMarker: any = null;

  // Enhanced overlay management
  private polygonOverlays: any[] = [];
  private circleOverlays: any[] = [];
  private markerOverlays: any[] = [];
  private labelOverlays: any[] = [];
  private selectedOverlay: any = null;

  // Overlap prevention settings
  private readonly MIN_OVERLAY_DISTANCE = 50; // meters
  private readonly ZOOM_SCALE_FACTOR = 0.1;
  flagImages = [
    '../../../assets/img/green.png',
    '../../../assets/img/red.png',
    '../../../assets/img/amber.png'
  ];

  constructor(
    private qrCodeService: QrCodeService,
    private plantService: PlantManagementService,
  ) {
    this.setCurrentUserRoleAndDetailsById();
    this.getPlants();
  }

  private setCurrentUserRoleAndDetailsById(): void {
    try {
      const userString = localStorage.getItem('user');
      if (!userString) {
        this.toast?.showErrorToast("User session invalid. Please log in again.");
        return;
      }
      const currentUser = JSON.parse(userString);
      this.loggedInPlantIds = currentUser?.plantIds?.map((id: any) => Number(id)) || [];
      const roleId = currentUser?.adminsRoleId;
      if (roleId === 2) {
        this.currentUserRole = 'plant_admin';
      }
    } catch (error) {
      console.error("Error parsing user data from localStorage:", error);
    }
  }

  ngAfterViewInit(): void {
    this.renderMap();
  }

  onMapTypeChange(): void {
    this.renderMap();
  }

  // Centralized function to render the map
  private renderMap(): void {
    const zones = Object.keys(this.zoneWiseQrCodes);
    if (this.selectedPlantId && !zones.length) {
      this.toast?.showErrorToast(`No zones found for the selected plant.`);
      console.warn("No zones found for the selected plant, but rendering the base map.");
    }

    // Always render the map, but only add overlays if zones exist.
    if (this.selectedMapType === 'circle') {
      this.initCircleMap(zones);
    } else {
      this.initPolygonMap(zones);
    }

    // Add legend if zones exist
    if (zones.length > 0) {
      // Small delay to ensure map is fully rendered
      setTimeout(() => {
        this.createLegend();
      }, 500);
    }
  }

  async onPlantSelectionChange(selectedPlantId: number | string | null): Promise<void> {
    try {
      console.log("Plant selected:", selectedPlantId);

      if (selectedPlantId === null) {
        this.selectedPlantId = 0;
        this.selectedPlantName = null;
        console.warn("No plant selected.");
        return;
      }

      const plantId = Number(selectedPlantId);

      if (isNaN(plantId)) {
        console.error("Invalid plant ID:", selectedPlantId);
        return;
      }

      const matchedPlant = this.availablePlants.find(item => item.id === plantId);

      if (!matchedPlant) {
        console.warn("No plant found for ID:", plantId);
        this.selectedPlantName = null;
        this.selectedPlantId = 0;
        this.renderMap(); // Re-render the map
        return;
      }

      this.selectedPlantId = plantId;
      this.selectedPlantName = matchedPlant.name;
      console.log("Selected Plant:", matchedPlant);

      await this.loadQrCodes(); // Load QR codes for the selected plant
      this.renderMap(); // Re-render the map

    } catch (error) {
      console.error("Error handling plant selection:", error);
    }
  }

  async getPlants(): Promise<void> {
    const queryParams = {
      sort: 'name,ASC',
      filter: ['enabled||eq||true'],
      limit: 1000,
    };

    try {
      const param = createAxiosConfig(queryParams);
      const response = await this.plantService.getPlants(param);
      let plants: Plant[] = response?.data ?? response ?? [];

      if (this.currentUserRole === 'plant_admin') {
        plants = plants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        if (plants.length > 0) {
          this.selectedPlantId = plants[0].id;
          this.onPlantSelectionChange(this.selectedPlantId);
        }
      }

      this.availablePlants = plants;
      console.log("this.availablePlants", this.availablePlants);

    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
    }
  }

  async loadQrCodes(): Promise<void> {
    if (!this.selectedPlantId) {
      console.warn("No plant selected. QR code loading skipped.");
      this.availableQrCodes = [];
      this.zoneWiseQrCodes = {};
      return;
    }

    const filter = [
      `plantId||eq||${this.selectedPlantId}`,
      'enabled||eq||true',
      'isDeleted||eq||false',
      'status||eq||1',
      'businessUnitId||eq||1',
    ];

    const params = createAxiosConfig({ filter, limit: 1000 });

    try {
      const response = await this.qrCodeService.getQrCode(params);
      const qrCodes = response ?? [];

      this.availableQrCodes = qrCodes;
      console.log("this.availableQrCodes", this.availableQrCodes);

      this.calculateZones(qrCodes);
    } catch (error) {
      console.error('Error fetching QR codes for zones:', error);
      this.availableQrCodes = [];
    }
  }

  calculateZones(qrResponse: any[]): void {
    this.zoneWiseQrCodes = {};

    if (!Array.isArray(qrResponse)) {
      console.warn("Invalid QR code response. Expected array.");
      return;
    }

    for (const qr of qrResponse) {
      if (!qr.lat || !qr.long || !qr.zone) continue;

      const zoneName = qr.zone.zoneName || 'unknown';

      this.center.lat = Number(qr.lat);
      this.center.lng = Number(qr.long);

      if (!this.zoneWiseQrCodes[zoneName]) {
        this.zoneWiseQrCodes[zoneName] = [];
      }

      this.zoneWiseQrCodes[zoneName].push(qr);
    }
    console.log("zoneWiseQrCodes", this.zoneWiseQrCodes);

  }
  selectedMapType: 'circle' | 'polygon' = 'circle';

  private initPolygonMap(zones: any): void {
    const mapContainer = this.apiMapDiv?.nativeElement;

    if (!mapContainer) {
      console.error("Map container element not found (#apiMapDiv).");
      return;
    }

    // Clear existing overlays
    this.clearAllOverlays();

    const map = new google.maps.Map(mapContainer, {
      center: this.center,
      zoom: this.zoomMap,
      mapId: environment.googleMapId
    });

    this.map = map;
    console.log("Enhanced polygon map rendering for zones:", zones);

    this.infoWindow = new google.maps.InfoWindow();
    map.addListener('click', () => {
      this.infoWindow?.close();
      this.currentlyOpenMarker = null;
      // Deselect any selected overlay
      if (this.selectedOverlay) {
        this.resetOverlayStyle(this.selectedOverlay);
        this.selectedOverlay = null;
      }
    });
    this.infoWindow.addListener('closeclick', () => {
      this.currentlyOpenMarker = null;
    });

    // const zones = Object.keys(this.zoneWiseQrCodes);
    // if (!zones.length) {
    //   console.warn("No zones found to render on the map.");
    //   return;
    // }

    const locations = zones.map((zoneName: string, index: number) => {
      this.zoneColors[zoneName] = this.colors[index % this.colors.length];

      const positions = this.zoneWiseQrCodes[zoneName]
        .map(item => {
          const lat = Number(item.lat);
          const lng = Number(item.long);
          if (isNaN(lat) || isNaN(lng)) return null;

          return {
            lat,
            lng,
            qrType: item.qrType?.title ?? 'N/A',
            plantName: this.selectedPlantName ?? 'N/A'
          };
        })
        .filter(Boolean) as { lat: number, lng: number, qrType: string, plantName: string }[];

      return {
        label: zoneName,
        positions,
        color: this.zoneColors[zoneName],
      };
    });

    // Apply overlap prevention
    const processedLocations = this.calculateOverlapPrevention(locations.map((loc: any, index: number) => ({
      ...loc,
      center: this.calculateLocationCenter(loc.positions),
      radius: this.calculateLocationRadius(loc.positions),
      index
    })));

    processedLocations.forEach((loc: any, index: number) => {
      if (loc.positions.length < 3) return;

      const bounds = new google.maps.LatLngBounds();
      loc.positions.forEach((p: any) => bounds.extend(p));
      const center = bounds.getCenter();

      // Enhanced padding with zoom-aware scaling
      const zoomFactor = Math.max(0.5, (this.zoomMap - 10) * this.ZOOM_SCALE_FACTOR);
      const paddingInMeters = 8 * zoomFactor;

      const paddedPositions = loc.positions.map((p: any) => {
        const originalPosition = new google.maps.LatLng(p.lat, p.lng);
        const heading = google.maps.geometry.spherical.computeHeading(center, originalPosition);
        const distance = google.maps.geometry.spherical.computeDistanceBetween(center, originalPosition);
        const newPosition = google.maps.geometry.spherical.computeOffset(center, distance + paddingInMeters, heading);
        return newPosition;
      });

      // Enhanced polygon styling with color scheme
      const colorScheme = this.polygonColors[index % this.polygonColors.length];
      const polygon = new google.maps.Polygon({
        map,
        paths: paddedPositions,
        strokeColor: colorScheme.stroke,
        strokeOpacity: 0.9,
        strokeWeight: 4,
        fillColor: colorScheme.fill,
        fillOpacity: 0.3,
        clickable: true,
        zIndex: 1
      });

      // Store polygon reference
      this.polygonOverlays.push(polygon);

      // Add enhanced hover effects
      polygon.addListener('mouseover', () => {
        if (this.selectedOverlay !== polygon) {
          polygon.setOptions({
            strokeWeight: 6,
            fillOpacity: 0.5,
            strokeOpacity: 1.0,
            fillColor: colorScheme.hover,
            zIndex: 10
          });
        }
      });

      polygon.addListener('mouseout', () => {
        if (this.selectedOverlay !== polygon) {
          polygon.setOptions({
            strokeWeight: 4,
            fillOpacity: 0.3,
            strokeOpacity: 0.9,
            fillColor: colorScheme.fill,
            zIndex: 1
          });
        }
      });

      // Add click handler for polygon selection
      polygon.addListener('click', (event: any) => {
        // Deselect previous overlay
        if (this.selectedOverlay && this.selectedOverlay !== polygon) {
          this.resetOverlayStyle(this.selectedOverlay);
        }

        // Select current polygon
        this.selectedOverlay = polygon;
        polygon.setOptions({
          strokeWeight: 8,
          fillOpacity: 0.6,
          strokeOpacity: 1.0,
          fillColor: colorScheme.hover,
          zIndex: 20
        });

        // Show enhanced info window
        const infoContent = this.createPolygonInfoContent(loc, event.latLng);
        this.infoWindow?.setContent(infoContent);
        this.infoWindow?.setPosition(event.latLng);
        this.infoWindow?.open(map);
      });

      // Enhanced Zone Label Marker
      // Use the same colorScheme from above
      const labelDiv = document.createElement('div');
      labelDiv.innerText = loc.label;
      Object.assign(labelDiv.style, {
        background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%)',
        padding: '8px 12px',
        borderRadius: '20px',
        fontSize: '13px',
        fontWeight: '600',
        border: `2px solid ${colorScheme.stroke}`,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        whiteSpace: 'nowrap',
        color: '#333',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        userSelect: 'none'
      });

      // Add hover effects to label
      labelDiv.addEventListener('mouseenter', () => {
        labelDiv.style.transform = 'scale(1.05)';
        labelDiv.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
        labelDiv.style.background = `linear-gradient(135deg, ${colorScheme.fill} 0%, ${colorScheme.hover} 100%)`;
        labelDiv.style.color = '#fff';
      });

      labelDiv.addEventListener('mouseleave', () => {
        labelDiv.style.transform = 'scale(1)';
        labelDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        labelDiv.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%)';
        labelDiv.style.color = '#333';
      });

      const labelMarker = new google.maps.marker.AdvancedMarkerElement({
        map,
        position: center.toJSON(),
        content: labelDiv,
        zIndex: 100
      });

      this.labelOverlays.push(labelMarker);

      loc.positions.forEach((position: any) => {
        const flagForThisZone = this.flagImages[index % this.flagImages.length];
        const markerContent = this.createEnhancedCustomMarker(flagForThisZone, colorScheme);

        const marker = new google.maps.marker.AdvancedMarkerElement({
          map,
          position,
          content: markerContent,
          zIndex: 50
        });

        this.markerOverlays.push(marker);

        // Enhanced marker hover effects
        markerContent.addEventListener('mouseenter', () => {
          markerContent.style.transform = 'scale(1.2)';
          markerContent.style.zIndex = '200';
        });

        markerContent.addEventListener('mouseleave', () => {
          markerContent.style.transform = 'scale(1)';
          markerContent.style.zIndex = '50';
        });

        marker.addListener('click', () => {
          const isSameMarker = this.currentlyOpenMarker === marker;

          if (isSameMarker) {
            this.infoWindow?.close();
            this.currentlyOpenMarker = null;
          } else {
            const infoContent = this.createEnhancedMarkerInfoContent(position, loc.label);
            this.infoWindow?.setContent(infoContent);
            this.infoWindow?.open(map, marker);
            this.currentlyOpenMarker = marker;
          }
        });
      });
    });

  }

  private initCircleMap(zones: any): void {
    const mapContainer = this.apiMapDiv?.nativeElement;

    if (!mapContainer) {
      console.error("Map container element not found (#apiMapDiv).");
      return;
    }

    // Clear existing overlays
    this.clearAllOverlays();

    const map = new google.maps.Map(mapContainer, {
      center: this.center,
      zoom: this.zoomMap,
      mapId: environment.googleMapId
    });

    this.map = map;

    this.infoWindow = new google.maps.InfoWindow();
    map.addListener('click', () => {
      this.infoWindow?.close();
      this.currentlyOpenMarker = null;
      // Deselect any selected overlay
      if (this.selectedOverlay) {
        this.resetOverlayStyle(this.selectedOverlay);
        this.selectedOverlay = null;
      }
    });
    this.infoWindow.addListener('closeclick', () => {
      this.currentlyOpenMarker = null;
    });

    const locations = zones.map((zoneName: string, index: number) => {
      this.zoneColors[zoneName] = this.colors[index % this.colors.length];

      const positions = this.zoneWiseQrCodes[zoneName]
        .map(item => {
          const lat = Number(item.lat);
          const lng = Number(item.long);
          if (isNaN(lat) || isNaN(lng)) return null;

          return {
            lat,
            lng,
            qrType: item.qrType?.title ?? 'N/A',
            plantName: this.selectedPlantName ?? 'N/A',
            zoneName: item.zone.zoneName ?? 'N/A'
          };
        })
        .filter(Boolean) as { lat: number, lng: number, qrType: string, plantName: string }[];

      return {
        label: zoneName,
        positions,
        color: this.zoneColors[zoneName],
      };
    });

    // Apply overlap prevention for circles
    const processedLocations = this.calculateOverlapPrevention(locations.map((loc: any, index: number) => ({
      ...loc,
      center: this.calculateLocationCenter(loc.positions),
      radius: this.calculateLocationRadius(loc.positions),
      index
    })));

    processedLocations.forEach((loc: any, index: number) => {
      if (loc.positions.length < 2) return;

      const bounds = new google.maps.LatLngBounds();
      loc.positions.forEach((p: any) => bounds.extend(p));
      const center = bounds.getCenter();

      let maxDistance = 0;
      loc.positions.forEach((p: any) => {
        const distance = google.maps.geometry.spherical.computeDistanceBetween(
          center,
          new google.maps.LatLng(p.lat, p.lng)
        );
        if (distance > maxDistance) {
          maxDistance = distance;
        }
      });

      // Enhanced radius calculation with zoom-aware scaling and minimum size
      const zoomFactor = Math.max(0.5, (this.zoomMap - 10) * this.ZOOM_SCALE_FACTOR);
      const baseRadius = Math.max(maxDistance + 8, 25); // Minimum 25m radius
      const radius = baseRadius * zoomFactor;

      // Enhanced circle styling with color scheme
      const colorScheme = this.polygonColors[index % this.polygonColors.length];
      const circle = new google.maps.Circle({
        map,
        center,
        radius,
        strokeColor: colorScheme.stroke,
        strokeOpacity: 0.9,
        strokeWeight: 3,
        fillColor: colorScheme.fill,
        fillOpacity: 0.25,
        clickable: true,
        zIndex: 1
      });

      // Store circle reference
      this.circleOverlays.push(circle);

      // Add enhanced hover effects
      circle.addListener('mouseover', () => {
        if (this.selectedOverlay !== circle) {
          circle.setOptions({
            strokeWeight: 5,
            fillOpacity: 0.4,
            strokeOpacity: 1.0,
            fillColor: colorScheme.hover,
            zIndex: 10
          });
        }
      });

      circle.addListener('mouseout', () => {
        if (this.selectedOverlay !== circle) {
          circle.setOptions({
            strokeWeight: 3,
            fillOpacity: 0.25,
            strokeOpacity: 0.9,
            fillColor: colorScheme.fill,
            zIndex: 1
          });
        }
      });

      // Add click handler for circle selection
      circle.addListener('click', (event: any) => {
        // Deselect previous overlay
        if (this.selectedOverlay && this.selectedOverlay !== circle) {
          this.resetOverlayStyle(this.selectedOverlay);
        }

        // Select current circle
        this.selectedOverlay = circle;
        circle.setOptions({
          strokeWeight: 6,
          fillOpacity: 0.5,
          strokeOpacity: 1.0,
          fillColor: colorScheme.hover,
          zIndex: 20
        });

        // Show enhanced info window
        const infoContent = this.createCircleInfoContent(loc, event.latLng, radius);
        this.infoWindow?.setContent(infoContent);
        this.infoWindow?.setPosition(event.latLng);
        this.infoWindow?.open(map);
      });

      // Enhanced Zone Label Marker for Circle
      // Use the same colorScheme from above
      const labelDiv = document.createElement('div');
      labelDiv.innerText = loc.label;
      Object.assign(labelDiv.style, {
        background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%)',
        padding: '8px 12px',
        borderRadius: '20px',
        fontSize: '13px',
        fontWeight: '600',
        border: `2px solid ${colorScheme.stroke}`,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        whiteSpace: 'nowrap',
        color: '#333',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        userSelect: 'none'
      });

      // Add hover effects to label
      labelDiv.addEventListener('mouseenter', () => {
        labelDiv.style.transform = 'scale(1.05)';
        labelDiv.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
        labelDiv.style.background = `linear-gradient(135deg, ${colorScheme.fill} 0%, ${colorScheme.hover} 100%)`;
        labelDiv.style.color = '#fff';
      });

      labelDiv.addEventListener('mouseleave', () => {
        labelDiv.style.transform = 'scale(1)';
        labelDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        labelDiv.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%)';
        labelDiv.style.color = '#333';
      });

      const labelMarker = new google.maps.marker.AdvancedMarkerElement({
        map,
        position: center.toJSON(),
        content: labelDiv,
        zIndex: 100
      });

      this.labelOverlays.push(labelMarker);

      // Place each QR marker with enhanced styling
      loc.positions.forEach((position: any) => {
        const flagForThisZone = this.flagImages[index % this.flagImages.length];
        const markerContent = this.createEnhancedCustomMarker(flagForThisZone, colorScheme);

        const marker = new google.maps.marker.AdvancedMarkerElement({
          map,
          position,
          content: markerContent,
          zIndex: 50
        });

        this.markerOverlays.push(marker);

        // Enhanced marker hover effects
        markerContent.addEventListener('mouseenter', () => {
          markerContent.style.transform = 'scale(1.2)';
          markerContent.style.zIndex = '200';
        });

        markerContent.addEventListener('mouseleave', () => {
          markerContent.style.transform = 'scale(1)';
          markerContent.style.zIndex = '50';
        });

        marker.addListener('click', () => {
          const isSameMarker = this.currentlyOpenMarker === marker;

          if (isSameMarker) {
            this.infoWindow?.close();
            this.currentlyOpenMarker = null;
          } else {
            const infoContent = this.createEnhancedMarkerInfoContent(position, loc.label);
            this.infoWindow?.setContent(infoContent);
            this.infoWindow?.open(map, marker);
            this.currentlyOpenMarker = marker;
          }
        });
      });
    });
  }



  private createEnhancedCustomMarker(imageSrc: string, colorScheme: any): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
      <div style="
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        transform-origin: center bottom;
      ">
        <div style="
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background: linear-gradient(135deg, ${colorScheme.fill} 0%, ${colorScheme.stroke} 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          border: 3px solid white;
        ">
          <img
            src="${imageSrc}"
            alt="Pin"
            style="width: 20px; height: 20px; filter: brightness(0) invert(1);"
          />
        </div>
        <div style="
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 8px solid white;
        "></div>
      </div>
    `;
    return container.firstElementChild as HTMLElement;
  }

  private createEnhancedMarkerInfoContent(position: any, zoneName: string): string {
    return `
      <div style="
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 16px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        min-width: 220px;
        max-width: 280px;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
          <div style="
            width: 10px;
            height: 10px;
            background: #fff;
            border-radius: 50%;
            margin-right: 8px;
          "></div>
          <h3 style="margin: 0; font-size: 15px; font-weight: 600;">QR Code Details</h3>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; margin-bottom: 8px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">Zone:</span>
            <strong>${zoneName}</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">QR Type:</span>
            <strong>${position.qrType}</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">Plant:</span>
            <strong>${position.plantName}</strong>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="opacity: 0.9;">Coordinates:</span>
            <strong>${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}</strong>
          </div>
        </div>

        <div style="font-size: 11px; opacity: 0.8; text-align: center;">
          📍 Individual QR Code Location
        </div>
      </div>
    `;
  }

  // Enhanced overlay management methods
  private clearAllOverlays(): void {
    // Clear polygons
    this.polygonOverlays.forEach(polygon => polygon.setMap(null));
    this.polygonOverlays = [];

    // Clear circles
    this.circleOverlays.forEach(circle => circle.setMap(null));
    this.circleOverlays = [];

    // Clear markers
    this.markerOverlays.forEach(marker => marker.map = null);
    this.markerOverlays = [];

    // Clear labels
    this.labelOverlays.forEach(label => label.map = null);
    this.labelOverlays = [];

    this.selectedOverlay = null;
  }

  private calculateOverlapPrevention(locations: any[]): any[] {
    const processedLocations = [...locations];

    for (let i = 0; i < processedLocations.length; i++) {
      for (let j = i + 1; j < processedLocations.length; j++) {
        const loc1 = processedLocations[i];
        const loc2 = processedLocations[j];

        if (this.checkOverlap(loc1, loc2)) {
          // Adjust position to prevent overlap
          processedLocations[j] = this.adjustOverlayPosition(loc1, loc2);
        }
      }
    }

    return processedLocations;
  }

  private checkOverlap(loc1: any, loc2: any): boolean {
    if (!loc1.center || !loc2.center) return false;

    const distance = google.maps.geometry.spherical.computeDistanceBetween(
      new google.maps.LatLng(loc1.center.lat, loc1.center.lng),
      new google.maps.LatLng(loc2.center.lat, loc2.center.lng)
    );

    const minDistance = (loc1.radius || 0) + (loc2.radius || 0) + this.MIN_OVERLAY_DISTANCE;
    return distance < minDistance;
  }

  private adjustOverlayPosition(loc1: any, loc2: any): any {
    const center1 = new google.maps.LatLng(loc1.center.lat, loc1.center.lng);
    const center2 = new google.maps.LatLng(loc2.center.lat, loc2.center.lng);

    const heading = google.maps.geometry.spherical.computeHeading(center1, center2);
    const distance = (loc1.radius || 0) + (loc2.radius || 0) + this.MIN_OVERLAY_DISTANCE;

    const newPosition = google.maps.geometry.spherical.computeOffset(center1, distance, heading);

    return {
      ...loc2,
      center: { lat: newPosition.lat(), lng: newPosition.lng() }
    };
  }

  private createLegend(): void {
    if (!this.map) return;

    const legend = document.createElement('div');
    legend.style.cssText = `
      background: rgba(255, 255, 255, 0.95);
      border-radius: 8px;
      padding: 12px;
      margin: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      font-family: Arial, sans-serif;
      font-size: 12px;
      max-width: 200px;
    `;

    const title = document.createElement('div');
    title.textContent = 'Zone Legend';
    title.style.cssText = 'font-weight: bold; margin-bottom: 8px; color: #333;';
    legend.appendChild(title);

    Object.keys(this.zoneColors).forEach((zoneName, index) => {
      const item = document.createElement('div');
      item.style.cssText = 'display: flex; align-items: center; margin-bottom: 4px;';

      const colorBox = document.createElement('div');
      const colorScheme = this.polygonColors[index % this.polygonColors.length];
      colorBox.style.cssText = `
        width: 16px;
        height: 16px;
        background-color: ${colorScheme.fill};
        border: 2px solid ${colorScheme.stroke};
        margin-right: 8px;
        border-radius: 2px;
      `;

      const label = document.createElement('span');
      label.textContent = zoneName;
      label.style.color = '#333';

      item.appendChild(colorBox);
      item.appendChild(label);
      legend.appendChild(item);
    });

    this.map.controls[google.maps.ControlPosition.LEFT_BOTTOM].push(legend);
  }

  private calculateLocationCenter(positions: any[]): any {
    if (positions.length === 0) return { lat: 0, lng: 0 };

    const bounds = new google.maps.LatLngBounds();
    positions.forEach(p => bounds.extend(p));
    const center = bounds.getCenter();

    return { lat: center.lat(), lng: center.lng() };
  }

  private calculateLocationRadius(positions: any[]): number {
    if (positions.length === 0) return 0;

    const center = this.calculateLocationCenter(positions);
    const centerLatLng = new google.maps.LatLng(center.lat, center.lng);

    let maxDistance = 0;
    positions.forEach(p => {
      const distance = google.maps.geometry.spherical.computeDistanceBetween(
        centerLatLng,
        new google.maps.LatLng(p.lat, p.lng)
      );
      maxDistance = Math.max(maxDistance, distance);
    });

    return maxDistance + 10; // Add buffer
  }

  private resetOverlayStyle(overlay: any): void {
    // Check if it's a polygon by looking for paths property
    if (overlay.getPaths) {
      const index = this.polygonOverlays.indexOf(overlay);
      const colorScheme = this.polygonColors[index % this.polygonColors.length];
      overlay.setOptions({
        strokeWeight: 4,
        fillOpacity: 0.3,
        strokeOpacity: 0.9,
        fillColor: colorScheme.fill,
        zIndex: 1
      });
    } else if (overlay.getRadius) { // Check if it's a circle
      const index = this.circleOverlays.indexOf(overlay);
      const colorScheme = this.polygonColors[index % this.polygonColors.length];
      overlay.setOptions({
        strokeWeight: 3,
        fillOpacity: 0.25,
        strokeOpacity: 0.9,
        fillColor: colorScheme.fill,
        zIndex: 1
      });
    }
  }

  private createPolygonInfoContent(location: any, _position?: any): string {
    const qrCount = location.positions.length;
    const area = this.calculatePolygonArea(location.positions);

    return `
      <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        min-width: 250px;
        max-width: 300px;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
          <div style="
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            margin-right: 8px;
          "></div>
          <h3 style="margin: 0; font-size: 16px; font-weight: 600;">${location.label}</h3>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; margin-bottom: 8px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">QR Codes:</span>
            <strong>${qrCount}</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">Area:</span>
            <strong>${area.toFixed(0)} m²</strong>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="opacity: 0.9;">Type:</span>
            <strong>Polygon Zone</strong>
          </div>
        </div>

        <div style="font-size: 11px; opacity: 0.8; text-align: center;">
          Click zone to select • Hover for preview
        </div>
      </div>
    `;
  }

  private createCircleInfoContent(location: any, _position: any, radius: number): string {
    const qrCount = location.positions.length;
    const area = Math.PI * radius * radius;

    return `
      <div style="
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 16px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        min-width: 250px;
        max-width: 300px;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
          <div style="
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            margin-right: 8px;
          "></div>
          <h3 style="margin: 0; font-size: 16px; font-weight: 600;">${location.label}</h3>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; margin-bottom: 8px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">QR Codes:</span>
            <strong>${qrCount}</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">Radius:</span>
            <strong>${radius.toFixed(0)} m</strong>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
            <span style="opacity: 0.9;">Area:</span>
            <strong>${area.toFixed(0)} m²</strong>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="opacity: 0.9;">Type:</span>
            <strong>Circle Zone</strong>
          </div>
        </div>

        <div style="font-size: 11px; opacity: 0.8; text-align: center;">
          Click zone to select • Hover for preview
        </div>
      </div>
    `;
  }

  private calculatePolygonArea(positions: any[]): number {
    if (positions.length < 3) return 0;

    // Convert to LatLng objects
    const path = positions.map(p => new google.maps.LatLng(p.lat, p.lng));

    // Calculate area using spherical geometry
    return google.maps.geometry.spherical.computeArea(path);
  }
}
