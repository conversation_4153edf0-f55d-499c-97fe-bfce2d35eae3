import { CommonModule } from '@angular/common';
import { Component, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { QrCodeService } from '../../services/qr-code/qr-code.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { environment } from '../../enviornments/enviornments';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';


interface Plant {
  id: number;
  name: string;
}

@Component({
  selector: 'app-heat-map',
  standalone: true,
  imports: [GoogleMapsModule, CommonModule, FormsModule, NgSelectModule, ToastMessageComponent],
  templateUrl: './heat-map.component.html',
  styleUrls: ['./heat-map.component.scss']
})
export class HeatMapComponent implements AfterViewInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  //getGoogleMapId = environment.googleMapId;
  @ViewChild('apiMapDiv') apiMapDiv!: ElementRef;
  availablePlants: Plant[] = [];
  selectedPlantId: number = 0;
  currentUserRole: string = '';
  loggedInPlantIds: number[] = [];
  selectedPlantName: any = [];
  zoomMap = 17;
  center: google.maps.LatLngLiteral = { lat: 28.5707078, lng: 77.5470732 };
  zoneColors: { [zoneId: string]: string } = {};
  colors = ['#e6194b', '#3cb44b', '#ffe119', '#4363d8', '#f58231', '#911eb4', '#46f0f0', '#fabebe'];

  availableQrCodes: any[] = [];
  zoneWiseQrCodes: { [zoneName: string]: any[] } = {};
  private map: google.maps.Map | null = null; // Reference to the map instance

  private infoWindow: google.maps.InfoWindow | undefined;
  private currentlyOpenMarker: google.maps.marker.AdvancedMarkerElement | null = null;
  flagImages = [
    '../../../assets/img/green.png',
    '../../../assets/img/red.png',
    '../../../assets/img/amber.png'
  ];

  constructor(
    private qrCodeService: QrCodeService,
    private plantService: PlantManagementService,
  ) {
    this.setCurrentUserRoleAndDetailsById();
    this.getPlants();
  }

  private setCurrentUserRoleAndDetailsById(): void {
    try {
      const userString = localStorage.getItem('user');
      if (!userString) {
        this.toast?.showErrorToast("User session invalid. Please log in again.");
        return;
      }
      const currentUser = JSON.parse(userString);
      this.loggedInPlantIds = currentUser?.plantIds?.map((id: any) => Number(id)) || [];
      const roleId = currentUser?.adminsRoleId;
      if (roleId === 2) {
        this.currentUserRole = 'plant_admin';
      }
    } catch (error) {
      console.error("Error parsing user data from localStorage:", error);
    }
  }

  ngAfterViewInit(): void {
    this.renderMap();
  }

  onMapTypeChange(): void {
    this.renderMap();
  }

  // Centralized function to render the map
  private renderMap(): void {
    const zones = Object.keys(this.zoneWiseQrCodes);
    if (this.selectedPlantId && !zones.length) {
      this.toast?.showErrorToast(`No zones found for the selected plant.`);
      console.warn("No zones found for the selected plant, but rendering the base map.");
    }

    // Always render the map, but only add overlays if zones exist.
    if (this.selectedMapType === 'circle') {
      this.initCircleMap(zones);
    } else {
      this.initPolygonMap(zones);
    }
  }

  async onPlantSelectionChange(selectedPlantId: number | string | null): Promise<void> {
    try {
      console.log("Plant selected:", selectedPlantId);

      if (selectedPlantId === null) {
        this.selectedPlantId = 0;
        this.selectedPlantName = null;
        console.warn("No plant selected.");
        return;
      }

      const plantId = Number(selectedPlantId);

      if (isNaN(plantId)) {
        console.error("Invalid plant ID:", selectedPlantId);
        return;
      }

      const matchedPlant = this.availablePlants.find(item => item.id === plantId);

      if (!matchedPlant) {
        console.warn("No plant found for ID:", plantId);
        this.selectedPlantName = null;
        this.selectedPlantId = 0;
        this.renderMap(); // Re-render the map
        return;
      }

      this.selectedPlantId = plantId;
      this.selectedPlantName = matchedPlant.name;
      console.log("Selected Plant:", matchedPlant);

      await this.loadQrCodes(); // Load QR codes for the selected plant
      this.renderMap(); // Re-render the map

    } catch (error) {
      console.error("Error handling plant selection:", error);
    }
  }

  async getPlants(): Promise<void> {
    const queryParams = {
      sort: 'name,ASC',
      filter: ['enabled||eq||true'],
      limit: 1000,
    };

    try {
      const param = createAxiosConfig(queryParams);
      const response = await this.plantService.getPlants(param);
      let plants: Plant[] = response?.data ?? response ?? [];

      if (this.currentUserRole === 'plant_admin') {
        plants = plants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        if (plants.length > 0) {
          this.selectedPlantId = plants[0].id;
          this.onPlantSelectionChange(this.selectedPlantId);
        }
      }

      this.availablePlants = plants;
      console.log("this.availablePlants", this.availablePlants);

    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
    }
  }

  async loadQrCodes(): Promise<void> {
    if (!this.selectedPlantId) {
      console.warn("No plant selected. QR code loading skipped.");
      this.availableQrCodes = [];
      this.zoneWiseQrCodes = {};
      return;
    }

    const filter = [
      `plantId||eq||${this.selectedPlantId}`,
      'enabled||eq||true',
      'isDeleted||eq||false',
      'status||eq||1',
      'businessUnitId||eq||1',
    ];

    const params = createAxiosConfig({ filter, limit: 1000 });

    try {
      const response = await this.qrCodeService.getQrCode(params);
      const qrCodes = response ?? [];

      this.availableQrCodes = qrCodes;
      console.log("this.availableQrCodes", this.availableQrCodes);

      this.calculateZones(qrCodes);
    } catch (error) {
      console.error('Error fetching QR codes for zones:', error);
      this.availableQrCodes = [];
    }
  }

  calculateZones(qrResponse: any[]): void {
    this.zoneWiseQrCodes = {};

    if (!Array.isArray(qrResponse)) {
      console.warn("Invalid QR code response. Expected array.");
      return;
    }

    for (const qr of qrResponse) {
      if (!qr.lat || !qr.long || !qr.zone) continue;

      const zoneName = qr.zone.zoneName || 'unknown';

      this.center.lat = Number(qr.lat);
      this.center.lng = Number(qr.long);

      if (!this.zoneWiseQrCodes[zoneName]) {
        this.zoneWiseQrCodes[zoneName] = [];
      }

      this.zoneWiseQrCodes[zoneName].push(qr);
    }
    console.log("zoneWiseQrCodes", this.zoneWiseQrCodes);

  }
  selectedMapType: 'circle' | 'polygon' = 'circle';

  private initPolygonMap(zones: any): void {
    const mapContainer = this.apiMapDiv?.nativeElement;

    if (!mapContainer) {
      console.error("Map container element not found (#apiMapDiv).");
      return;
    }

    const map = new google.maps.Map(mapContainer, {
      center: this.center,
      zoom: this.zoomMap,
      //mapId: this.getGoogleMapId
    });

    console.log("Polygon map rendering for zones:", zones);
    console.warn('Polygon map feature is not yet implemented.');
    
    this.infoWindow = new google.maps.InfoWindow();
    map.addListener('click', () => {
      this.infoWindow?.close();
      this.currentlyOpenMarker = null;
    });
    this.infoWindow.addListener('closeclick', () => {
      this.currentlyOpenMarker = null;
    });

    // const zones = Object.keys(this.zoneWiseQrCodes);
    // if (!zones.length) {
    //   console.warn("No zones found to render on the map.");
    //   return;
    // }

    const locations = zones.map((zoneName: string, index: number) => {
      this.zoneColors[zoneName] = this.colors[index % this.colors.length];

      const positions = this.zoneWiseQrCodes[zoneName]
        .map(item => {
          const lat = Number(item.lat);
          const lng = Number(item.long);
          if (isNaN(lat) || isNaN(lng)) return null;

          return {
            lat,
            lng,
            qrType: item.qrType?.title ?? 'N/A',
            plantName: this.selectedPlantName ?? 'N/A'
          };
        })
        .filter(Boolean) as { lat: number, lng: number, qrType: string, plantName: string }[];

      return {
        label: zoneName,
        positions,
        color: this.zoneColors[zoneName],
      };
    });

    locations.forEach((loc: any, index: number) => {
      if (loc.positions.length < 3) return;

      const bounds = new google.maps.LatLngBounds();
      loc.positions.forEach((p: any) => bounds.extend(p));
      const center = bounds.getCenter();

      const paddingInMeters = 5; // Define the padding distance. Adjust as needed.

      const paddedPositions = loc.positions.map((p: any) => {
        const originalPosition = new google.maps.LatLng(p.lat, p.lng);

        // Calculate the direction (heading) from the center to the point
        const heading = google.maps.geometry.spherical.computeHeading(center, originalPosition);

        // Calculate the distance from the center to the point
        const distance = google.maps.geometry.spherical.computeDistanceBetween(center, originalPosition);

        // Calculate the new, padded position by offsetting from the center
        const newPosition = google.maps.geometry.spherical.computeOffset(center, distance + paddingInMeters, heading);

        return newPosition; // Returns a google.maps.LatLng object
      });

      // Draw the polygon using the new padded vertices
      new google.maps.Polygon({
        map,
        paths: paddedPositions,
        strokeColor: '#adb5bd',          //'#808080',
        strokeOpacity: 1.0,     // Make border more visible
        strokeWeight: 3,        // Make border thicker
        fillColor: '#FFFFFF',
        fillOpacity: 0.25,      // Adjust fill transparency as needed
      });

      // Add Zone Label Marker
      const labelDiv = document.createElement('div');
      labelDiv.innerText = loc.label;
      Object.assign(labelDiv.style, {
        background: '#fff',
        padding: '4px 8px',
        borderRadius: '6px',
        fontSize: '12px',
        fontWeight: 'bold',
        border: '1px solid #888',
        boxShadow: '0 2px 6px rgba(0,0,0,0.5)',
        whiteSpace: 'nowrap',
      });

      new google.maps.marker.AdvancedMarkerElement({
        map,
        position: center.toJSON(),
        content: labelDiv,
      });

      loc.positions.forEach((position: any) => {
        const flagForThisZone = this.flagImages[index % this.flagImages.length];
        const markerContent = this.createCustomMarker(flagForThisZone);

        const marker = new google.maps.marker.AdvancedMarkerElement({
          map,
          position,
          content: markerContent,
        });

        marker.addListener('click', () => {
          const isSameMarker = this.currentlyOpenMarker === marker;

          if (isSameMarker) {
            this.infoWindow?.close();
            this.currentlyOpenMarker = null;
          } else {
            const infoContent = `
            <div style="
              background-color: white;
              padding: 8px 15px;
              max-width: 170px;
              border-radius: 8px;
              box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
              font-family: Arial, sans-serif;
              text-align: left;
            ">
              <div style="font-size: 12px; color: #333;">
                <strong>QR Type:</strong> ${position.qrType}
              </div>
              <div style="font-size: 12px; color: #333; margin-top: 5px;">
                <strong>Plant Name:</strong> ${position.plantName}
              </div>
            </div>
          `;
            this.infoWindow?.setContent(infoContent);
            this.infoWindow?.open(map, marker);
            this.currentlyOpenMarker = marker;
          }
        });
      });
    });

  }

  private initCircleMap(zones: any): void {
    const mapContainer = this.apiMapDiv?.nativeElement;

    if (!mapContainer) {
      console.error("Map container element not found (#apiMapDiv).");
      return;
    }

    const map = new google.maps.Map(mapContainer, {
      center: this.center,
      zoom: this.zoomMap,
     // mapId: this.getGoogleMapId
    });

    this.infoWindow = new google.maps.InfoWindow();
    map.addListener('click', () => {
      this.infoWindow?.close();
      this.currentlyOpenMarker = null;
    });
    this.infoWindow.addListener('closeclick', () => {
      this.currentlyOpenMarker = null;
    });

    const locations = zones.map((zoneName: string, index: number) => {
      this.zoneColors[zoneName] = this.colors[index % this.colors.length];

      const positions = this.zoneWiseQrCodes[zoneName]
        .map(item => {
          const lat = Number(item.lat);
          const lng = Number(item.long);
          if (isNaN(lat) || isNaN(lng)) return null;

          return {
            lat,
            lng,
            qrType: item.qrType?.title ?? 'N/A',
            plantName: this.selectedPlantName ?? 'N/A',
            zoneName: item.zone.zoneName ?? 'N/A'
          };
        })
        .filter(Boolean) as { lat: number, lng: number, qrType: string, plantName: string }[];

      return {
        label: zoneName,
        positions,
        color: this.zoneColors[zoneName],
      };
    });

    locations.forEach((loc: any, index: number) => {
      if (loc.positions.length < 2) return;

      const bounds = new google.maps.LatLngBounds();
      loc.positions.forEach((p: any) => bounds.extend(p));
      const center = bounds.getCenter();

      let maxDistance = 0;
      loc.positions.forEach((p: any) => {
        const distance = google.maps.geometry.spherical.computeDistanceBetween(
          center,
          new google.maps.LatLng(p.lat, p.lng)
        );
        if (distance > maxDistance) {
          maxDistance = distance + 4;
        }
      });

      const radius = maxDistance;

      // Draw Circle
      new google.maps.Circle({
        map,
        center,
        radius,
        strokeColor: '#adb5bd',
        strokeOpacity: 1,
        strokeWeight: 1,
        fillColor: '#FFFFFF',
        fillOpacity: 0.15,
      });

      // Add Zone Label Marker
      const labelDiv = document.createElement('div');
      labelDiv.innerText = loc.label;
      Object.assign(labelDiv.style, {
        background: '#fff',
        padding: '4px 8px',
        borderRadius: '6px',
        fontSize: '12px',
        fontWeight: 'bold',
        border: '1px solid #888',
        boxShadow: '0 2px 6px rgba(0,0,0,0.5)',
        whiteSpace: 'nowrap',
      });

      new google.maps.marker.AdvancedMarkerElement({
        map,
        position: center.toJSON(),
        content: labelDiv,
      });

      // Place each QR marker
      loc.positions.forEach((position: any) => {
        const flagForThisZone = this.flagImages[index % this.flagImages.length];
        const markerContent = this.createCustomMarker(flagForThisZone);

        const marker = new google.maps.marker.AdvancedMarkerElement({
          map,
          position,
          content: markerContent,
        });

        marker.addListener('click', () => {
          const isSameMarker = this.currentlyOpenMarker === marker;

          if (isSameMarker) {
            this.infoWindow?.close();
            this.currentlyOpenMarker = null;
          } else {
            const infoContent = `
            <div style="
              background-color: white;
              padding: 4px 8px;
              max-width: 200px;
              border-radius: 8px;
              font-family: Arial, sans-serif;
              text-align: left;
            ">
              <div style="font-size: 12px; color: #333;">
                <strong>QR Type:</strong> ${position.qrType}
              </div>
              <div style="font-size: 12px; color: #333; margin-top: 5px;">
                <strong>Plant Name:</strong> ${position.plantName}
              </div>
              <div style="font-size: 12px; color: #333; margin-top: 5px;">
                <strong>Zone Name:</strong> ${position.zoneName}
              </div>
            </div>
          `;
            this.infoWindow?.setContent(infoContent);
            this.infoWindow?.open(map, marker);
            this.currentlyOpenMarker = marker;
          }
        });
      });
    });
  }

  private createCustomMarker(imageSrc: string): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
      <div style="
        border-radius: 4px;
        display: flex;
        align-items: center;
        cursor: pointer;
      ">
        <img
          src="${imageSrc}"
          alt="Pin"
          style="width: 26px; height: 26px;"
        />
      </div>
    `;
    return container.firstElementChild as HTMLElement;
  }
}
