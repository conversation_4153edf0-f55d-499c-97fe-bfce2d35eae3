:host {
    display: block;
    width: 100%;
    height: 100%;
}

.map-view-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
  display: flex;
  gap: 15px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.map-type-select,
.form-select {
  width: 200px; /* Adjust as needed */
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff;
}

:host ::ng-deep .gm-style-iw-c button {
    display: none !important;
}

/* Enhanced map styling */
:host ::ng-deep .gm-style-iw {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
}

:host ::ng-deep .gm-style-iw-d {
  overflow: hidden !important;
  border-radius: 12px !important;
}

/* Custom marker animations */
.enhanced-marker {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center bottom;
}

.enhanced-marker:hover {
  transform: scale(1.1);
  z-index: 1000;
}

/* Zone label styling */
.zone-label {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

/* Legend styling */
.map-legend {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  border: 1px solid rgba(255,255,255,0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
    gap: 10px;
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
  }

  .form-select,
  .map-type-select {
    width: 100%;
  }
}

/* Enhanced info window styling */
:host ::ng-deep .gm-style-iw-chr {
  display: none !important;
}

:host ::ng-deep .gm-style-iw-tc {
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

/* Smooth transitions for overlays */
:host ::ng-deep .enhanced-polygon {
  transition: all 0.3s ease;
}

:host ::ng-deep .enhanced-circle {
  transition: all 0.3s ease;
}