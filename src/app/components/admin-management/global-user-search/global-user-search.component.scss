/* Card styling */
.card {
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  margin-top: 10px;
  overflow: hidden;
}

.card-body {
  height: 70vh;
  overflow: auto;
  padding: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-size: 12px;
  text-align: center;
}

/* Filter button styling */
.filter-button {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.filter-button:hover {
  transform: scale(1.1);
}

/* Table styling */
.custom-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  table-layout: fixed;
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  text-align: left;
  font-size: 12px;
  border: 1px solid #dee2e6;
  vertical-align: top;
}

.table-header {
  background-color: #f8f9fa; /* Changed to a solid light background */
  color: #495057; /* Changed to a darker color */
  font-weight: bold;
  text-align: center;
}

.table-header th {
  border: none;
  color: #495057; /* Changed to a darker color */
}

/* Column widths */
.col-avatar {
  width: 12%;
  min-width: 80px;
}

.col-admin-details {
  width: 48%;
  min-width: 300px;
}

.col-plant-info {
  width: 40%;
  min-width: 250px;
}

/* Sort header styling */
.sort-header {
  color: #495057 !important; /* Changed to a darker color */
  font-weight: bold;
  text-decoration: none !important;
  border: none;
  background: none;
  padding: 0;
  font-size: 12px;
}

.sort-header:hover {
  color: #f8f9fa !important;
}

.sort-header i {
  margin-left: 4px;
  font-size: 10px;
}

/* User avatar styling */
.user-avatar {
  width: 35px;
  height: 35px;
  object-fit: cover;
  border: 1px solid #dee2e6;
}

.user-id {
  font-size: 10px;
  color: #6c757d;
}

/* Details container styling */
.details-container {
  font-size: 11px;
}

.label-value {
  display: flex; /* Make it a flex container */
  align-items: baseline; /* Align items to their baselines */
  gap: 5px; /* Add a small gap between label and value */
  margin-bottom: 8px;
  line-height: 1.4;
}

.label-value:last-child {
  margin-bottom: 0;
}

.value-text {
  color: #495057;
  word-break: break-word;
}

/* Badge styling */
.badge {
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 12px;
}

/* Plant badges styling */
.plant-badges-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.plant-badge {
  display: inline-block;
  padding: 2px 6px;
  font-size: 9px;
  font-weight: 500;
  color: white;
  border-radius: 8px;
  margin: 1px;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Filter button styling */
.filter-button {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.filter-button:hover {
  transform: scale(1.1);
}

/* Filter container styling */
.filter-container {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.filter-container .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.filter-container .form-control,
.filter-container .form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  font-size: 14px;
}

.filter-container .form-control:focus,
.filter-container .form-select:focus {
  border-color: #0b74b0;
  box-shadow: 0 0 0 0.2rem rgba(11, 116, 176, 0.25);
}

/* Button styling */
.adani-btn {
  background: linear-gradient(90deg, #0b74b0, #75479c, #bd3681);
  border: none;
  color: white;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.adani-btn:hover {
  background: linear-gradient(90deg, #095a8a, #5d3a7d, #9a2d68);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.adani-btn:disabled {
  background: #6c757d;
  color: white;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .card-body {
    height: auto;
    max-height: 60vh;
  }

  .card-header .row {
    flex-direction: column;
  }

  .card-header .col-md-4,
  .card-header .col-md-8 {
    width: 100%;
    margin-bottom: 1rem;
  }

  .card-header .col-md-8:last-child {
    margin-bottom: 0;
  }

  .action-buttons-section {
    padding: 0.75rem;
  }

  .action-buttons-section .row {
    flex-direction: column;
  }

  .action-buttons-section .col-md-6 {
    width: 100%;
    margin-bottom: 0.5rem;
    text-align: left !important;
  }

  .custom-table {
    font-size: 10px;
  }

  .custom-table th,
  .custom-table td {
    padding: 6px 8px;
  }

  .user-avatar {
    width: 30px;
    height: 30px;
  }

  .plant-badge {
    font-size: 8px;
    padding: 1px 4px;
    max-width: 80px;
  }
}

@media (max-width: 576px) {
  .col-avatar,
  .col-admin-details,
  .col-plant-info {
    width: auto;
    min-width: auto;
  }
  
  .custom-table {
    font-size: 9px;
  }
  
  .details-container {
    font-size: 10px;
  }
  
  .search-section .row {
    flex-direction: column;
  }
  
  .search-section .col-md-4 {
    margin-top: 0.5rem;
    text-align: left !important;
  }
}

/* Loading and empty state styling */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-muted {
  color: #6c757d !important;
}

/* Card footer styling */
.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* Dropdown styling */
.dropdown-toggle::after {
  margin-left: 0.5rem;
}

.dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 14px;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:disabled {
  color: #6c757d;
  background-color: transparent;
  cursor: not-allowed;
}
