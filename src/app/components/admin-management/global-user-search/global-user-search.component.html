<app-toast-message></app-toast-message>

<!-- Main Search Card -->
<div class="card custom-card" id="global-user-search">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6 class="">Global User Search</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">Avatar</th>
                        <th scope="col" class="col-admin-details">Admin Details</th>
                        <th scope="col" class="col-plant-info">Plant Information</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Loading users...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && userList.length === 0">
                        <td colspan="3" class="text-center p-4 text-muted">
                            No users found matching the criteria.
                        </td>
                    </tr>
                    <tr *ngFor="let user of userList">
                        <td class="text-center">
                            <div class="user-id mb-2 fw-bold">ID: {{user.id}}</div>
                            <img [src]="user.profilePicture || '../../../assets/svg/Avatar.svg'" class="img-thumbnail rounded-circle" alt="Avatar">
                        </td>
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{user.firstName}} {{user.lastName}}</span></p>
                                <p class="label-value">
                                    <strong>Role:</strong>
                                    <span class="value-text">
                                        <span *ngIf="user.adminsRole?.id === 1" class="badge bg-primary">Super Admin</span>
                                        <span *ngIf="user.adminsRole?.id === 2" class="badge bg-warning">Plant Admin</span>
                                        <span *ngIf="user.adminsRole?.id === 3" class="badge bg-secondary">User</span>
                                        <span *ngIf="user.adminsRole?.id === 6" class="badge bg-info">Global Admin</span>
                                    </span>
                                </p>
                                <p class="label-value">
                                    <strong>Status:</strong>
                                    <span class="value-text">
                                        <span *ngIf="user.status === 1" class="badge bg-success">Active</span>
                                        <span *ngIf="user.status === 0" class="badge bg-danger">Inactive</span>
                                        <span *ngIf="user.status === 2" class="badge bg-warning">Rejected</span>
                                        <span *ngIf="user.status === null || user.status === undefined" class="badge bg-secondary">Unknown</span>
                                    </span>
                                </p>
                                <p class="label-value">
                                    <strong>Deletion Status:</strong>
                                    <span class="value-text">
                                        <span *ngIf="user.isDeleted === false" class="badge bg-success">Not Deleted</span>
                                        <span *ngIf="user.isDeleted === true" class="badge bg-warning">Temporarily Deleted</span>
                                        <span *ngIf="user.isDeleted === null || user.isDeleted === undefined" class="badge bg-secondary">Unknown</span>
                                    </span>
                                </p>
                                <p class="label-value">
                                    <strong>Application Access:</strong>
                                    <span class="value-text">
                                        <span *ngIf="user.applicationId === 0 || user.applicationId === 1" class="badge bg-info">BOG</span>
                                        <span *ngIf="user.applicationId === 2" class="badge bg-primary">WBI</span>
                                        <span *ngIf="user.applicationId === 3" class="badge bg-success">BOG/WBI</span>
                                        <span *ngIf="user.applicationId === null || user.applicationId === undefined || (user.applicationId !== 0 && user.applicationId !== 1 && user.applicationId !== 2 && user.applicationId !== 3)" class="badge bg-secondary">Unknown</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{user.email}}</span></p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{user.contactNumber}}</span></p>
                                <p class="label-value"><strong>Date of Birth:</strong> <span class="value-text">{{user.dob | date:'dd-MMM-yyyy'}}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(user.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ user.gender }}</span>
                                    </span>
                                </p>
                                <p class="label-value" *ngIf="user.createdTimestamp != null">
                                    <strong>Created Date:</strong> <span class="value-text">{{user.createdTimestamp | date:'dd-MMM-yyyy'}}</span>
                                </p>
                                <p class="label-value" *ngIf="user.lastLogin != null">
                                    <strong>Last Login:</strong> <span class="value-text">{{user.lastLogin | date:'dd-MMM-yyyy HH:mm'}}</span>
                                </p>
                            </div>
                        </td>
                        <td>
                          <div class="details-container">
                            <p class="label-value">
                              <strong>Plants:</strong>
                              <span class="value-text">
                                <div class="plant-badges-container">
                                  <ng-container *ngIf="user.plant?.length; else noPlants">
                                    <span *ngFor="let plant of user.plant" class="plant-badge" [ngClass]="{'bg-primary': plant.id % 6 === 0, 'bg-secondary': plant.id % 6 === 1, 'bg-success': plant.id % 6 === 2, 'bg-info': plant.id % 6 === 3, 'bg-warning': plant.id % 6 === 4, 'bg-danger': plant.id % 6 === 5}">
                                      {{ plant.name }}
                                    </span>
                                  </ng-container>
                                  <ng-template #noPlants><span class="text-muted">No plants</span></ng-template>
                                </div>
                              </span>
                            </p>
                            <p class="label-value"><strong>Department:</strong> <span class="value-text">{{user.department?.title || 'N/A'}}</span></p>
                            <p class="label-value"><strong>Designation:</strong> <span class="value-text">{{user.designation?.title || 'N/A'}}</span></p>
                          </div>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination Footer -->
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" 
                       [totalItems]="totalItems" 
                       [itemsPerPage]="itemsPerPage"
                       (pageChange)="onPageChange($event)">
        </app-pagination>
    </div>
</div>

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Users'" *ngIf="isFilterOffcanvasOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterFirstName">First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="First Name"
                           [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #firstName="ngModel" [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <span *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterLastName">Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Last Name"
                           [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #lastName="ngModel" [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <span *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEmail">Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="Email"
                           [(ngModel)]="filters.email" name="email" maxlength="200">
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterMobile">Mobile Number</label>
                    <input type="text" id="filterMobile" class="form-control" placeholder="Mobile Number"
                           [(ngModel)]="filters.mobile" name="mobile" maxlength="10" pattern="^[0-9]+$"
                           #mobile="ngModel" [ngClass]="{'is-invalid': mobile.invalid && (mobile.dirty || mobile.touched)}">
                    <div *ngIf="mobile.invalid && (mobile.dirty || mobile.touched)" class="invalid-feedback">
                        <span *ngIf="mobile.errors?.['pattern']">Mobile number should contain only digits.</span>
                    </div>
                </div>



                <!-- Action Buttons -->
                <div class="col-12 mt-4">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn adani-btn">
                            <i class="bi bi-funnel me-1"></i> Apply Filters
                        </button>
                        <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
                            <i class="bi bi-x-circle me-1"></i> Clear All Filters
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
