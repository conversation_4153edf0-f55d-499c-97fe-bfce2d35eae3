import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
    GLOBAL_ADMIN: 'global_admin'
};

// Interface for filter structure
interface UserSearchFilter {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    mobile?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}



@Component({
  selector: 'app-global-user-search',
  imports: [
    CommonModule,
    FormsModule,
    PaginationComponent,
    OffcanvasComponent,
    ToastMessageComponent
  ],
  templateUrl: './global-user-search.component.html',
  styleUrl: './global-user-search.component.scss'
})
export class GlobalUserSearchComponent implements OnInit, OnDestroy {
    public componentRoles = ROLES;

    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    private destroy$ = new Subject<void>();

    // Search and filter properties
    userList: any[] = [];
    listLoading = false;
    isFilterOffcanvasOpen = false;

    // Pagination properties
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // Filter properties
    filters: UserSearchFilter = {
        firstName: null,
        lastName: null,
        email: null,
        mobile: null,
        sortField: 'id',
        sortDirection: 'DESC'
    };



    // User role and permissions
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    constructor(
        private adminService: AdminService
    ) {}

    async ngOnInit() {
        this.setCurrentUserRoleAndDetailsById();
        this.loadUsers(this.currentPage);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private setCurrentUserRoleAndDetailsById() {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid.");
                return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) {
                this.currentUserRole = ROLES.SUPER_ADMIN;
            } else if (roleId === 6) {
                this.currentUserRole = ROLES.GLOBAL_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    this.toast?.showErrorToast("Plant Admin has no assigned plants.");
                }
            } else {
                this.currentUserRole = '';
                this.toast?.showErrorToast("Invalid user role.");
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error loading user session.");
        }
    }



    // Search functionality

    // Filter functionality
    openFilterModal() {
        this.isFilterOffcanvasOpen = true;
    }

    closeFilterModal() {
        this.isFilterOffcanvasOpen = false;
    }

    applyFilters() {
        this.currentPage = 1;
        this.loadUsers(this.currentPage);
        this.closeFilterModal();
    }

    clearFilters() {
        this.filters = {
            firstName: null,
            lastName: null,
            email: null,
            mobile: null,
            sortField: 'id',
            sortDirection: 'DESC'
        };
        this.applyFilters();
    }

    // Pagination
    onPageChange(page: number) {
        this.currentPage = page;
        this.loadUsers(page);
    }

    // Sorting
    sortBy(field: string) {
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC';
        }
        this.loadUsers(this.currentPage);
    }

    // Data loading methods
    private async loadUsers(page: number) {
        this.listLoading = true;
        this.userList = [];

        try {
            const filterConditions = this.buildFilterParams();
            const data = {
                page: page,
                limit: this.itemsPerPage,
                sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
                filter: filterConditions,
                join: ['adminsRole', 'department', 'designation', 'plant']
            };

            const config = createAxiosConfig(data);
            const response = await this.adminService.getAdmin(config);

            if (response && response.data) {
                this.userList = response.data.map((user: any) => {
                    // Debug logging to understand the structure (only for first user)
                    if (user.id === response.data[0]?.id) {
                        console.log('User plant data structure:', {
                            plant: user.plant,
                            plantUsers: user.plantUsers,
                            fullUser: user
                        });
                    }

                    // The plant data should come directly from the 'plant' join
                    // It should be an array of plant objects
                    let plants = [];
                    if (user.plant && Array.isArray(user.plant)) {
                        plants = user.plant;
                    } else if (user.plant && typeof user.plant === 'object') {
                        // If plant is a single object, convert to array
                        plants = [user.plant];
                    }

                    return {
                        ...user,
                        plant: plants
                    };
                });
                this.totalItems = response.total || 0;
            } else {
                this.userList = [];
                this.totalItems = 0;
            }
        } catch (error) {
            console.error('Error loading users:', error);
            this.toast?.showErrorToast('Error loading users. Please try again.');
            this.userList = [];
            this.totalItems = 0;
        } finally {
            this.listLoading = false;
        }
    }

    private buildFilterParams(): string[] {
        const filterConditions: string[] = [];

        // Exclude super admin (ID = 1) from search results
        filterConditions.push('id||ne||1');

        // Individual field filters
        if (this.filters.firstName?.trim()) {
            filterConditions.push(`firstName||$contL||${this.filters.firstName.trim()}`);
        }
        if (this.filters.lastName?.trim()) {
            filterConditions.push(`lastName||$contL||${this.filters.lastName.trim()}`);
        }
        if (this.filters.email?.trim()) {
            filterConditions.push(`email||$contL||${this.filters.email.trim()}`);
        }
        if (this.filters.mobile?.trim()) {
            filterConditions.push(`contactNumber||$contL||${this.filters.mobile.trim()}`);
        }

        return filterConditions;
    }




}
