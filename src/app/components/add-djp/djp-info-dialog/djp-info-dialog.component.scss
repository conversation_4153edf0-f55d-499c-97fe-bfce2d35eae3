.main-dialog-style {
    min-width: 500px;
    max-width: 80vw;
    max-height: 70vh;
    font-family: 'Arial', sans-serif;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.with-border {
    padding-bottom: 8px;
    margin-bottom: 16px;
}
.spacer {
    flex-grow: 1;
}

.fixed-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    border-bottom: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    margin: 0;
    height: 48px;
    min-height: 48px;
    margin-bottom: 6px;
}

.dialog-title {
    margin: 0;
    font-size: 1.1rem;
    color: white !important;
}

.two-column-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}


.column {
    width: 330px;
    min-height: 145px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item {
    padding: 5px 5px;
    border-radius: 6px;
    word-wrap: break-word;
    white-space: normal;
}

.label {
    font-weight: bold;
    margin-right: 8px;
    font-size: 14px;
}

.custom-expansion-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.panel-title {
    display: flex;
    align-items: center;
    gap: 50px;
    white-space: nowrap;
}

.title-index {
    font-weight: bold;
}

.title-email,
.title-number,
.title-index,
.title-name {
    font-weight: bold;
    font-size: 14px;
}

.title-name {
    width: 190px;
}

.title-email {
    width: 275px;
}

.title-number {
    width: 100px;
}

.cross-button {
    font-size: 1.9rem;
    line-height: 1;
    height: 32px;
    width: 32px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white !important;
    transition: background 0.2s;
    outline: none;
}

.close-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
}

.panel-title-row {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
}

.panel-title-row>span {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.title-number {
    margin-right: 10px;
}

.no-data-found {
    text-align: center;
    padding: 24px;
    color: #666;
    flex-shrink: 0;
    padding-top: 70px;
}

.clamp-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4.5em;
}

.tooltipStyle {
    display: inline;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
    text-wrap: auto;
}

.dropdown-card {
    background-color: white;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin-left: 5px;
    margin-right: 5px;
    border: 1px solid #ddd;
    border-bottom: 1px solid #DADADA;

    >.dropdown-content {
        width: 97%;
        margin: 0 auto;

        >.dropdown-header {
            padding: 10px 0;

            >h5 {
                font-weight: 600;
            }
        }
    }
}

.btn-link {
    width: 4.8%;
}

.hl {
    border-bottom: 1px solid #DADADA
}

.panelFontStyle {
    font-size: 14px;
}