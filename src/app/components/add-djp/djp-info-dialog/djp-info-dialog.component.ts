import { CommonModule } from '@angular/common';
import { Component, Inject, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { MatTooltip } from '@angular/material/tooltip';

interface SimpleOption {
  value: number;
  label: string;
}

@Component({
  selector: 'app-djp-info-dialog',
  imports: [CommonModule, MatDialogModule, MatTableModule, MatIcon, MatProgressSpinnerModule, MatButtonModule, MatTooltip, MatExpansionModule, ToastMessageComponent],
  templateUrl: './djp-info-dialog.component.html',
  styleUrl: './djp-info-dialog.component.scss'
})
export class DjpInfoDialogComponent {

  criticalityOptions: SimpleOption[] = [{ value: 1, label: 'Low' }, { value: 2, label: 'Medium' }, { value: 3, label: 'High' }, { value: 4, label: 'Critical' }];
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  panelOpenState = false;
  noDataMessage: boolean = false;
  selectedIndex: number = -1;
  expandPanelData = [
    {
      name: '',
      email: '',
      actionTaken: '',
      description: '',
      deviation: '',
      riskLevel: '',
      violation: '',
      workPermit: '',
      contactNumber: '',
      expanded: false
    }
  ];
  displayedColumns: string[] = ['DjpId', 'djpTitle', 'isDeleted'];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { djpFeedbackData: any },
    private dialogRef: MatDialogRef<DjpInfoDialogComponent>,
  ) {
  }

  isTruncated(text: string): boolean {
    return text ? text.length > 120 : false;
  }

  ngOnInit() {
    if (this.data.djpFeedbackData) {
      const observationResponse = Array.isArray(this.data.djpFeedbackData)
        ? this.data.djpFeedbackData
        : [this.data.djpFeedbackData];

      if (observationResponse.length !== 0) {
        this.expandPanelData = observationResponse.map((item: any) => ({
          name: item?.admin ? `${item?.admin.firstName}, ${item?.admin.lastName}` : 'NA',
          email: item?.admin ? item?.admin?.email : 'NA',
          contactNumber: item?.admin ? item?.admin?.contactNumber : 'NA',
          actionTaken: item?.ActionTaken ? item?.ActionTaken : 'NA',
          description: item?.Description ? item?.Description : 'NA',
          deviation: item?.Deviation ? item?.Deviation : 'NA',
          riskLevel: item?.RiskLevel ? this.getCriticalityLabel(item?.RiskLevel) : 'NA',
          violation: item?.Violation ? item?.Violation : 'NA',
          workPermit: item?.WorkPermit ? this.getCriticalityLabel(item?.WorkPermit) : 'NA',
          expanded: false
        }));
        this.noDataMessage = false
      }
      else {
        this.noDataMessage = true;
        this.toast?.showErrorToast('Failed to load observation of djp.');
      }
    }
    else {
      this.toast?.showErrorToast('Failed to load observation of djp.');
    }
  }

  togglePanel(index: number, event: Event) {
    event.stopPropagation();
    this.expandPanelData[index].expanded = !this.expandPanelData[index].expanded;
  }

  getCriticalityLabel(value: number | null | undefined | string): string {
    if (value === null || value === undefined) return '';
    const option = this.criticalityOptions.find(opt => opt.value == value);
    return option?.label ?? '';
  }

  djpCrossClick() {
    this.dialogRef.close();
  }

  toggleCollapse(index: number) {
    if (this.selectedIndex === index) {
      this.selectedIndex = -1;
    } else {
      this.selectedIndex = index;
    }
  }
}
